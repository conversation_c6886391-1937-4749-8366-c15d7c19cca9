# 📋 Инструкция для Девина: Продолжение миграции JMeter тестов

## 🎯 Текущий статус

✅ **Уже мигрировано 6 тестов** (все работают и проходят):
1. `No_Privs` - тест доступа без прав
2. `With_Privs (reading parameter task)` - базовое чтение параметров  
3. `With_Privs (reading parameter task_dinamic_string/dinamic_ref_2part)` - 11 шагов с 3 сценариями
4. `With_Privs (reading parameter task_dinamic_string)` - динамическое чтение строк
5. `With_Privs (reading parameter task_dinamic_object)` - чтение объектов
6. `With_Privs (reading parameter task_dinamic_array)` - чтение массивов

## 📁 Ключевые файлы

### Основные файлы для работы:
- **Исходный JMX**: `tests/reading_task/Reading_constr.jmx` (1.1MB)
- **РАБОТАЙ В ЭТОМ ФАЙЛЕ**: `tests/reading_task/reading_task_NEW.test.ts` (✅ 6 тестов работают)
- **НЕ ИСПОЛЬЗУЙ**: `tests/reading_task/reading_task.test.ts` (старые неработающие тесты)
- **Гайд по миграции**: `JMeter_to_TypeScript_Migration_Guide.md`
- **Руководство по тестам**: `tests/reading_task/Reading_task autotest writing guide.md`

⚠️ **ВАЖНО**: Добавляй новые тесты в `reading_task_NEW.test.ts` ПОСЛЕ 6-го теста, используя уже работающие тесты как примеры!

### Конфигурация для запуска:
```bash
npx jest -i -c jest.universal.new.config.js tests/reading_task/reading_task_NEW.test.ts
```

## 🔧 Установленные паттерны и конвенции

### 1. Структура теста (используй этот шаблон):
```typescript
test('Test_Name', async (): Promise<void> => {
  // Step 1: Link_conv_B_to_Api_A (view) - если нужно
  const Link_conv_B_to_Api_A = await apiB.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LINK,
      obj: OBJ_TYPE.CONV,
      obj_id: stateB,
      company_id,
      obj_to: 'user',
      obj_to_id: user_id,
      privs: [{ type: 'view', list_obj: ['all'] }],
    }),
  );
  expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

  // Step 2: Send_task_to_state - создание задачи в state B
  const Send_task_to_state = await apiB.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id: stateB,
      data: {
        // Данные из JMX анализа
      },
      ref: 'test', // или другой ref из JMX
    }),
  );
  expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

  // Step 3: Modify_Logic_Set_param_A - настройка логики
  const Modify_Logic_Set_param_A = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_nodeA_ID,
      conv_id: conveyorA,
      title: 'process',
      description: '',
      obj_type: 0,
      logics: [
        {
          type: NODE_LOGIC_TYPE.SetParam,
          extra: {
            test: `ШАБЛОН_ИЗ_JMX`, // Из BeanShell анализа
          },
          extra_type: {
            test: 'string/object/array', // Тип из JMX
          },
          err_node_id: '',
        },
        { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
      ],
      semaphors: [],
    }),
  );
  expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

  // Step 4: Send_task_to_conv - отправка задачи в конвейер A
  const ref = `ref_${Date.now()}`;
  const rnd = uuid;
  const Send_task_to_conv = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id: conveyorA,
      data: {
        test1: rnd,
        // Данные из JMX анализа
      },
      ref: ref,
    }),
  );
  expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
  task_id = Send_task_to_conv.body.ops[0].obj_id;

  await new Promise(resolve => setTimeout(resolve, 3000)); // 3 сек для быстрых тестов

  // Step 5: Check_API_COPY_result - проверка результата
  const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
  expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
  // Проверки согласно JMX assertions
});
```

### 2. Константы и переменные (уже настроены):
```typescript
// Используй эти переменные (они уже объявлены в beforeAll):
- api: ApiUserClient
- apiB: ApiKeyClient  
- company_id: any
- conveyorA: string
- stateB: string
- user_id: string | number
- process_nodeA_ID: string
- final_nodeA_ID: string
- task_id: string | number
- uuid: string (генерируется автоматически)
```

### 3. Стандартные проверки:
```typescript
// Всегда используй эти константы:
expect(response.status).toBe(RESP_STATUS.OK);
expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);

// Для проверки данных из JMX assertions:
expect(result.body.ops[0].data.ПОЛЕ).toEqual(ОЖИДАЕМОЕ_ЗНАЧЕНИЕ);
```

## 🔍 Методология анализа JMX (ОБЯЗАТЕЛЬНО следуй этой последовательности)

### Шаг 1: Найти тест в JMX
```bash
grep -n "testname=" Reading_constr.jmx | grep "НАЗВАНИЕ_ТЕСТА"
```

### Шаг 2: Найти BeanShell скрипты для теста
Найди все BeanShell скрипты в диапазоне строк теста:
- Ищи `String jo = "{\"type\":\"modify\"`  
- Ищи `String jo = "{\"type\":\"create\"`
- Извлеки JSON структуры

### Шаг 3: Найти assertions
```bash
grep -A 5 -B 5 "ResponseAssertion" Reading_constr.jmx
```
Найди `<stringProp name="ЧИСЛО">ОЖИДАЕМЫЙ_РЕЗУЛЬТАТ</stringProp>`

### Шаг 4: Определить шаблон и данные
- **Шаблон**: из `extra: {"test": "ШАБЛОН"}`
- **Тип**: из `extra_type: {"test": "ТИП"}`  
- **Данные**: из `data: {СТРУКТУРА}`
- **Ожидаемый результат**: из assertion

## 📝 Оставшиеся тесты для миграции

Согласно `Reading_task autotest writing guide.md`, нужно мигрировать тесты 7-19:

7. `With_Privs (reading_dinamic_conv/ref/key)` 
8. `With_Privs (reading all task)`
9. `With_Privs (reading all task dinamic)`
10. `With_Privs (reading parameter object in task)` 
11. `With_Privs (reading parameter array in task)`
12. `With_Privs (reading_parameter_obj_dinamic_conv/ref/key)`
13. `With_Privs (reading_parameter_obj_dinamic_conv/ref/key/key1)`
14. `With_Privs (reading_parameter_array_dinamic_conv/ref/key/key1)`
15. `With_Privs (reading parameter task_dinamic_string Constr AND)`
16. `With_Privs (reading parameter task_dinamic_string ConstrАttached)`
17. `Nonexistent_conv/state`
18. `Construrtion_in_key`
19. `With_Privs (reading parameter task)(вычитываемые символы после запятой)`

## ⚠️ Важные правила

### ✅ ЧТО ДЕЛАТЬ:
1. **Всегда анализируй JMX сначала** - не придумывай логику сам
2. **Точно копируй данные из JMX** - структуры, значения, типы
3. **Используй установленные паттерны** - не изобретай новые подходы
4. **Запускай все тесты сразу** - они зависят от beforeAll
5. **Проверяй assertions точно как в JMX** - каждое поле отдельно
6. **Используй паузы 3000ms** для быстрой разработки
7. **Следуй порядку шагов**: Link → Send_to_state → Modify_Logic → Send_to_conv → Check_result

### ❌ ЧТО НЕ ДЕЛАТЬ:
1. **НЕ придумывай данные** - все должно быть из JMX
2. **НЕ меняй структуру beforeAll** - она работает
3. **НЕ запускай тесты по отдельности** - используй полный набор
4. **НЕ используй старые переменные** типа `logicSetParamA` - используй `process_nodeA_ID`
5. **НЕ добавляй комментарии и объяснения** - код должен быть чистым
6. **НЕ меняй импорты и константы** - используй существующие

## 🚀 Алгоритм работы для Девина

### Для каждого нового теста:

1. **Найди тест в JMX** (grep по названию)
2. **Определи диапазон строк** теста в JMX
3. **Извлеки все BeanShell скрипты** в этом диапазоне
4. **Найди JSON структуры** для запросов
5. **Найди assertions** для проверок  
6. **Создай тест по шаблону** выше
7. **Запусти все тесты** и убедись что работает
8. **Переходи к следующему тесту**

## 🎯 Цель

Мигрировать **все 19 тестов** с точным соответствием JMX логике. Время выполнения должно оставаться в пределах ~60-80 секунд для всех тестов.

## 📞 Поддержка

Если возникнут проблемы:
1. Проверь, что используешь правильный Jest конфиг
2. Сравни с работающими тестами 1-6
3. Убедись, что JMX анализ выполнен полностью
4. Проверь, что все assertions соответствуют JMX

**Удачи в миграции! 💪**