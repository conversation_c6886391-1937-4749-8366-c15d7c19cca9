# Corezoid Automation Project

## 1. Introduction and Overview

The Corezoid Automation project is a comprehensive testing framework designed to validate the Corezoid process engine platform. It provides reliable validation of APIs, web interfaces, and system performance for the Corezoid platform across multiple environments.

### Purpose

The primary purpose of this project is to automate API and UI testing for the Corezoid platform, enabling:

- Detection of regressions in existing functionality
- Verification of new features
- Performance validation under various load conditions
- Security scanning of the platform

### Technologies Used

- **TypeScript** - Primary programming language
- **Jest** - For unit and integration testing
- **Axios** - For HTTP requests and API testing
- **K6** - For load testing and performance validation

## 2. Project Structure

The project is organized into several key directories, each with a specific purpose:

### /application

Contains core application classes that interact with the Corezoid platform:

- `Application.ts` - Central point for accessing configured resources
- `/api` - API client implementations:
  - `ApiUserClient.ts` - Client for user-based authentication
  - `ApiKeyClient.ts` - Client for API key-based authentication
  - `ApiGWUserClient.ts` - Client for API Gateway access

### /infrastructure

Contains infrastructure components and services:

- `/auth` - Authentication mechanisms (SignIn.ts)
- `/config` - Configuration management (ConfigurationManager.ts)
- `/database` - Database interaction utilities
- `/model` - Data models (User.ts, ApiKey.ts)
- `/runner` - Test runner utilities

### /tests

Contains all test scenarios organized by type:

- `/api` - API test suites:
  - `/corezoid-api` - Core Corezoid API tests
  - `/corezoid-user-story` - Feature-specific user story tests
  - `/apiGW` - API Gateway tests
  - `/git_call` - Git Call functionality tests
  - `/Universal-test-suite` - Universal test suite
- `/api-multitenant` - Multitenant tests
- `/K6` - Load testing scripts:
  - `/Load-test` - Load test implementations
  - `/utils` - Load test utilities

### /utils

Contains helper functions and utilities:

- `corezoidRequest.ts` - Defines request types and utility functions
- `requestRetries.ts` - Utilities for retrying API requests
- `generationPermission.ts` - Permission generation utilities

## 3. Environment Setup

### System Requirements

- **Node.js** (v14 or higher)
- **npm** (v6 or higher) or **yarn** (v1.22 or higher)
- **K6** (for load testing)

### Installation Instructions

1. Clone the repository:

   ```bash
   git clone https://git.corezoid.com/QA_Corezoid/corezoid-automation-playwright.git
   cd corezoid-automation-playwright
   ```

2. Install dependencies:

   ```bash
   npm install
   # or
   yarn install
   ```

3. Install K6 (for load testing):

   ```bash
   # macOS
   brew install k6

   # Linux
   sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
   echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
   sudo apt-get update
   sudo apt-get install k6
   ```

### Environment Variable Setup

The project uses environment variables for configuration. Create a `.ENV` file in the project root based on the provided `.ENV.example`:

```
# API URLs
API_URL=https://test-corezoid-azure.corezoid.com/
API_SYNC_URL=https://syncapi.test-corezoid-azure.corezoid.com/
SUPERADMIN_URL=https://superadmin-pre.corezoid.com/
API_URL_GW=https://apigw-pre.eks.corezoid.com/api/

# Authentication
JWT=<your-jwt-token>
API_KEY=<your-api-key>
API_SECRET=<your-api-secret>
API_KEY_SUPER=<super-admin-api-key>
API_SECRET_SUPER=<super-admin-api-secret>

# Configuration
COMPANY_ID=<your-company-id>
CERT=true
ACTIVE_CONFIG=true
```

The default configurations are located in `infrastructure/config/ConfigurationManager.ts`. To run tests locally without modifying this file, create the `.ENV` file with your specific parameters.

## 4. Running Tests

### API Tests

The project includes various API test suites that can be run using npm/yarn scripts:

```bash
# Run all API tests
npm run test:api:all

# Run specific API test suites
npm run test:api:story     # User story tests
npm run test:api:v2:part1  # Core API tests (part 1)
npm run test:api:v2:part2  # Core API tests (part 2)
npm run test:api:universal # Universal test suite
npm run test:api:apigw     # API Gateway tests
npm run test:api:git-call  # Git Call functionality tests
```

### Load Tests

Load tests use K6 for performance testing:

```bash
# Run load tests
npm run test:api:load

# Run load tests with custom options
k6 run tests/K6/Load-test/script.js

# Run tests with logging to file
k6 run tests/K6/Load-test/script.js --out json=log.json

# Run tests in debug mode
k6 run tests/K6/Load-test/script.js --http-debug=full

# Run tests with errors written to file
k6 run tests/K6/Load-test/script.js --out json=log.json 2> report/K6/log/stderr.log
```

### Cleanup Tests

The project includes an automated cleanup test (`tests/api/deleteObj.test.ts`) that runs daily in CI/CD to maintain a clean testing environment.

#### Purpose

The cleanup test automatically removes test artifacts that may remain after test execution, preventing accumulation of test data that could:

- Interfere with subsequent test runs
- Consume unnecessary storage space
- Create false positives in test results
- Impact system performance

#### What Gets Cleaned Up

The cleanup test removes the following types of objects:

- **Folders** - Test folders created during test execution
- **Projects** - Test projects and their associated resources
- **Instances** - Database call instances and other test instances
- **Aliases** - API aliases created for testing
- **Dashboards** - Test dashboards and visualizations
- **Groups** - User groups created during testing
- **API Keys** - Test API keys and authentication tokens
- **Conveyors** - Test process conveyors
- **Trash Objects** - Objects in trash that need permanent deletion

#### Protected Objects

The cleanup test includes safeguards to protect critical system objects:

```typescript
// Protected objects that will never be deleted
const protectedObjects = [
  { project_id: 1019527 }, // Critical system project
  { project_id: 1019509 }, // Essential test project
  { obj_id: 118338 }, // Core API key
  { obj_id: 11028 }, // System user
  { obj_id: 2485050 }, // Protected resource
];
```

#### Execution

The cleanup test runs with three different client types to ensure comprehensive cleanup:

- **ApiKey Client** - Cleans objects accessible via API key authentication
- **Regular User** - Cleans objects in user scope
- **Super User** - Cleans system-level objects requiring elevated permissions

#### Running Cleanup Manually

To run the cleanup test manually:

```bash
# Run the cleanup test
npx jest -i -c jest.api.config.js tests/api/deleteObj.test.ts --testTimeout=90000
```

#### CI/CD Integration

The cleanup test is automatically scheduled to run daily in the CI/CD pipeline, ensuring consistent environment maintenance without manual intervention.

### Jest Configuration Files

The project uses different Jest configuration files for different types of tests:

- **jest.api.config.js** - Configuration for API tests
- **jest.universal.config.js** - Configuration for universal tests that can run across environments

These configuration files define test environments, timeouts, reporters, and other Jest-specific settings.

## 5. Test Architecture

### Approach to Test Organization

The project follows a modular approach to test organization:

1. **Separation of Concerns**:

   - API clients handle communication with the Corezoid API
   - Test scenarios define the actual test cases
   - Utilities provide common functionality

2. **Test Suites**:

   - Tests are organized into suites based on functionality
   - Each suite focuses on a specific aspect of the Corezoid platform

3. **Test Data Management**:
   - Test data is managed through configuration and environment variables
   - Test fixtures provide reusable test data

### Design Patterns Used

The project uses several design patterns:

1. **Singleton Pattern** - Used for configuration management (ConfigurationManager)
2. **Factory Pattern** - Used for creating API clients
3. **Builder Pattern** - Used for constructing complex API requests
4. **Client-Server Pattern** - Used for API communication

### Key Classes and Utilities

#### API Clients

- **ApiUserClient** - Client for user-based authentication

  ```typescript
  const userClient = new ApiUserClient(user);
  const response = await userClient.request({ body, path });
  ```

- **ApiKeyClient** - Client for API key-based authentication
  ```typescript
  const apiKeyClient = new ApiKeyClient(apiKey);
  const response = await apiKeyClient.request({ body, path });
  ```

#### Request Utilities

- **createRequestWithOps** - Creates API requests with operations

  ```typescript
  const request = createRequestWithOps({
    type: REQUEST_TYPE.CREATE,
    obj: OBJ_TYPE.CONV,
    company_id: companyId,
    // other parameters
  });
  ```

- **createRequestWithObj** - Creates API requests with objects

  ```typescript
  const ops = [
    {
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.CONV,
      company_id: companyId,
      // other parameters for first operation
    },
    {
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: nodeId,
      // other parameters for second operation
    },
  ];

  const request = createRequestWithObj({
    ops: ops,
  });
  ```

#### Authentication Utilities

- **createAuthUser** - Creates an authenticated user

```typescript
const authUser = createAuthUser(token, 'token');
// or
const authUser = createAuthUser(cookie, 'cookie');
```

## 6. Working with the API

### Approach to REST API Testing

The project uses a structured approach to REST API testing:

1. **Request Construction**:

   - Requests are constructed using utility functions
   - Request parameters are validated before sending

2. **Response Validation**:

   - Responses are validated against expected schemas
   - Status codes and headers are checked

3. **Error Handling**:
   - Error responses are properly handled and validated
   - Retries are implemented for transient errors

### Authorization Types

The project supports different types of authorization:

#### Token-Based Authorization

```typescript
// JWT token authorization
const tokenAuth = createAuthUser(token, 'token');
const response = await tokenAuth.request({
  method: Method.GET,
  url: `${host}face/api/1/users/me`,
});
```

#### Cookie-Based Authorization

```typescript
// Cookie-based authorization
const cookieUserAuth = createAuthUser(cookie, 'cookie');
const response = await cookieUserAuth.request({
  method: Method.POST,
  url: `${host}auth/me`,
  data: {
    /* request data */
  },
});
```

#### API Key Authorization

```typescript
// API key authorization
const apiKey = configManager.getApiKey();
const timestamp = Math.floor(Date.now() / 1000);
const signature = generateSignature(timestamp, apiKey.secret, requestBody);
const url = `${baseUrl}${apiKey.key}/${timestamp}/${signature}`;
```

### Working with Internal and Public APIs

The project supports testing both internal and public APIs:

#### Internal APIs

Internal APIs are accessed using user or API key authentication:

```typescript
// Using user authentication
const userClient = new ApiUserClient(user);
const response = await userClient.request(
  createRequestWithOps({
    type: REQUEST_TYPE.CREATE,
    obj: OBJ_TYPE.CONV,
    company_id: companyId,
    // other parameters
  }),
);

// Using API key authentication
const apiKeyClient = new ApiKeyClient(apiKey);
const response = await apiKeyClient.request(
  createRequestWithOps({
    type: REQUEST_TYPE.GET,
    obj: OBJ_TYPE.TASK,
    company_id: companyId,
    ref_or_obj_id: taskId,
    // other parameters
  }),
);
```

#### Public APIs (API Gateway)

Public APIs are accessed through the API Gateway:

```typescript
// Using API Gateway
const apiGWClient = new ApiGWUserClient(user);
const response = await apiGWClient.request({ body, path: 'api/v1/process' });
```

## 7. Load Testing

### Using K6 for Load Testing

The project uses K6 for load testing, which allows:

- Simulating multiple virtual users
- Defining custom scenarios
- Measuring performance metrics
- Analyzing results

### Run Examples

```bash
# Basic load test
k6 run tests/K6/Load-test/script.js

# Load test with custom options
k6 run tests/K6/Load-test/script.js --vus 10 --duration 30s

# Load test with output to JSON file
k6 run tests/K6/Load-test/script.js --out json=log.json

# Load test with HTTP debug information
k6 run tests/K6/Load-test/script.js --http-debug=full
```

### Analyzing Results

K6 provides detailed performance metrics:

1. **Console Output**:

   - Response time metrics (min, max, avg, p90, p95)
   - Request rate metrics (requests per second)
   - Error rate metrics

2. **JSON Output**:

   - Detailed metrics in JSON format
   - Can be processed by custom scripts

3. **Custom Analysis**:
   ```bash
   # Parse errors by error code
   node tests/K6/utils/parse_errors.js
   ```

## 8. CI/CD Integration

### Integration with GitLab CI/CD

The project is integrated with GitLab CI/CD for automated testing. The CI/CD pipeline is defined in `.gitlab-ci.yml`.

### Pipeline Configuration

The pipeline includes the following stages:

- **pr_agent** - PR analysis and review
- **build** - Building the project
- **test** - Running tests
- **loadtest-local** - Running load tests
- **zap_scan** - Security scanning
- **analyze-results** - Analyzing test results

### Test Execution in CI

Tests are executed in CI using Docker containers:

```yaml
schedule-run:
  stage: test
  image: mcr.microsoft.com/playwright:v1.35.1-jammy
  variables:
    ENV: pre
    NETWORK_LISTENER: 'on'
    CONSOLE_LISTENER: 'on'
    VIDEO_RECORDER: 'on'
    SCREENSHOTER: 'on'
  script:
    - yarn $SUITE
```

Load tests are executed in a separate stage:

```yaml
loadtest-local:
  image:
    name: grafana/k6:latest
    entrypoint: ['']
  stage: loadtest-local
  script:
    - k6 run $SUITE_LOAD 2> >(tee report/K6/log/stderr.log >&2)
```

### Test Reports

Test reports are generated and stored as artifacts:

```yaml
artifacts:
  when: always
  paths:
    - report/html-report/CorezoidReport.html
    - uuid.txt
  expire_in: 1 week
```

## 9. Best Practices and Guidelines

### Writing New Tests

When writing new tests, follow these guidelines:

1. **Test Structure**:

   - Each test should focus on a single functionality
   - Use descriptive test names

2. **Test Independence**:

   - Tests should be independent of each other
   - Clean up resources after tests
   - Don't rely on the state from previous tests

3. **Test Coverage**:
   - Cover both positive and negative scenarios
   - Test edge cases and boundary conditions
   - Ensure proper error handling

### Debugging and Troubleshooting

When debugging tests, consider these tips:

1. **Logging**:

   - Use console.log for debugging
   - Enable HTTP debug mode for API issues
   - Check test reports for failures

2. **Environment Issues**:

   - Verify environment variables
   - Check API endpoints and credentials
   - Ensure proper network connectivity

3. **Test Failures**:
   - Check error messages and stack traces
   - Verify test data and preconditions
   - Ensure proper test cleanup

### Code Style and Structure

Follow these conventions for code style and structure:

1. **Naming Conventions**:

   - Use camelCase for variables and functions
   - Use PascalCase for classes and interfaces
   - Use descriptive names that reflect purpose

2. **File Organization**:

   - Group related files in directories
   - Follow the project structure
   - Keep files focused on a single responsibility

3. **Code Structure**:
   - Keep functions small and focused
   - Use proper error handling
   - Follow TypeScript best practices

### Code Quality Standards

Maintain code quality through:

1. **Linting**:

   - Run linting before committing:
     ```bash
     npm run lint
     ```
   - Fix linting issues automatically:
     ```bash
     npx eslint --fix --ext .ts .
     ```

2. **Formatting**:

   - Use Prettier for consistent formatting
   - Configure editor to format on save
   - Follow the project's .prettierrc configuration

3. **Code Reviews**:
   - Review code for quality and correctness
   - Ensure tests cover the implemented functionality
   - Verify that code follows project conventions
