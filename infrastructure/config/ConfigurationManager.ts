import axios from 'axios';
import { DataBase } from '../database/DataBase';
import { ApplicationConfig } from '../interface/ApplicationConfig';
import { Api<PERSON>ey } from '../model/ApiKey';
import { User } from '../model/User';
import { warn, error } from '../../support/utils/logger';
require('dotenv').config();

let account_host: string;
let ws_host: string;

const users = [
  new User(
    'test',
    process.env.LOGIN || '<EMAIL>',
    process.env.PASS || 'Password_08',
    [{ id: process.env.COMPANY_ID || 'i738314881' }], // || 'i738314881' Account ID: 12384
    false,
    12384,
  ),
  new User(
    'test2',
    process.env.LOGIN_2 || '<EMAIL>', // вторым пользователем всегда нужен superadmin
    process.env.PASS_2 || 'Password_06',
    [{ id: process.env.COMPANY_ID || 'i738314881' }], // || 'i738314881' Account ID: 12385
    true,
    12385,
  ),
  new User(
    'test3',
    process.env.LOGIN_3 || '<EMAIL>',
    process.env.PASS_3 || 'Test123456$',
    [{ id: process.env.COMPANY_ID || '5f372ef1-11fe-4ec1-acf9-30e6cf3ba178' }], // || 'i283490699'
    false,
    2936,
  ),
  new User(
    'test4',
    process.env.LOGIN_4 || '<EMAIL>',
    process.env.PASS_4 || 'Test123456$',
    [{ id: process.env.COMPANY_ID || 'i283490699' }],
    false,
    13477,
  ),
  new User(
    'test5Tacts',
    process.env.LOGIN_5 || '<EMAIL>', //49790
    process.env.PASS_5 || 'Test123456$',
    [{ id: process.env.COMPANY_ID || '69a18293-98ee-4457-9556-2937a178285d' }],
    false,
    123,
  ),
  new User(
    'test6Multitenant',
    process.env.LOGIN_6 || '<EMAIL>',
    process.env.PASS_6 || 'Test12345!',
    [{ id: process.env.COMPANY_ID || 'i000000000' }],
    true,
    2,
  ),
  new User(
    'test7Git_call',
    process.env.LOGIN_7 || '<EMAIL>',
    process.env.PASS_7 || 'Test123456!',
    [{ id: process.env.COMPANY_ID || 'i738314881' }],
    false,
    3685,
  ),
  new User(
    'test8',
    process.env.LOGIN_8 || '<EMAIL>',
    process.env.PASS_8 || 'Password_0101',
    [{ id: process.env.COMPANY_ID || 'a1e663fe-b2da-495c-83de-08397cb341b5' }],
    false,
    117310,
  ),
  new User(
    'test5Tacts',
    process.env.LOGIN_5 || '<EMAIL>', // Gmail Password - Password_0101
    process.env.PASS_5 || 'Password_0101',
    [{ id: process.env.COMPANY_ID || '69a18293-98ee-4457-9556-2937a178285d' }],
    false,
    130300,
  ),
  new User(
    'test5Tacts',
    process.env.LOGIN_5 || '<EMAIL>', // Gmail Password - Password_0101
    process.env.PASS_5 || 'Password_0101',
    [{ id: process.env.COMPANY_ID || '69a18293-98ee-4457-9556-2937a178285d' }],
    false,
    133169,
  ),
];

export class ConfigurationManager {
  private static instance: ConfigurationManager;
  private users = users;
  private config: ApplicationConfig | null = null;

  private constructor() {
    this.initializeConfig();
  }

  private async initializeConfig(): Promise<void> {
    if (!this.config) {
      await this.getCorezoidConfig();
    }
  }

  static getConfiguration(): ConfigurationManager {
    if (!this.instance) {
      this.instance = new ConfigurationManager();
      this.instance.initializeConfig().catch((err): void => {
        warn('Failed to initialize config:', err);
      });
    }
    return this.instance;
  }

  public async initialize(): Promise<void> {
    await this.initializeConfig();
  }

  public getCompanyId(id: string | undefined) {
    return id === 'null' ? null : id;
  }

  public rejectUnauthorized(): string {
    const certificate = process.env.CERT || 'true';
    return certificate;
  }

  public getUrl(): string {
    const url = process.env.BASE || 'https://pre.corezoid.com/';
    return url[url.length - 1] === '/' ? url : `${url}/`;
  }

  public getApiUrl(): string {
    const url = process.env.API_URL || 'https://admin-pre.corezoid.com/';
    return url[url.length - 1] === '/' ? url : `${url}/`;
  }

  public getApiSyncUrl(): string {
    const url = process.env.API_SYNC_URL || 'https://capisync-pre.corezoid.com/';
    return url[url.length - 1] === '/' ? url : `${url}/`;
  }

  public getSuperadminUrl(): string {
    const url = process.env.SUPERADMIN_URL || 'https://superadmin-pre.corezoid.com/';
    return url[url.length - 1] === '/' ? url : `${url}/`;
  }

  public getWsUrl(): [string, string] {
    const host = ws_host || 'ws-pre.corezoid.com';
    const url = `wss://${host}`;
    const path = `${this.config?.web_settings.path.ws || '/ws'}`;
    return [url, path];
  }

  public getSSUrl(): string {
    const url = process.env.SS_URL || (account_host ? `https://${account_host}/` : `https://account.pre.corezoid.com/`);
    return url[url.length - 1] === '/' ? url : `${url}/`;
  }

  public getApiGWUrl(): string {
    const url = process.env.API_URL_GW || 'https://api-apigw-pre.eks.corezoid.com/';
    return url[url.length - 1] === '/' ? url : `${url}/`;
  }

  public getActorizerUrl(): string {
    const url = process.env.ACTORIZER_URL || 'https://actoraizer-pre.corezoid.com/';
    return url[url.length - 1] === '/' ? url : `${url}/`;
  }

  public getDataGPT(): { url: string; token: string } {
    const url = 'https://api.openai.com/v1/chat/completions';
    const token =
      'Bearer ********************************************************************************************************************************************************************';
    return { url, token };
  }

  public ssCallback(): { url: string; token: string } {
    const url = 'https://admin-pre.corezoid.com/api/2/plugins/single_account/callback';
    const token = 'Basic NWNiNThmYWFhMjcxMGYzZTJhMDAwMDAyOmR1QXV1eWQydE1yNHVtNWJzam9JVXFSSW45UkJMVnZx';
    return { url, token };
  }

  public getJwtApi(id: number): string {
    const authorization = [
      process.env.JWT ||
        'Simulator eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.SOJx6R3v6ULcnQY-qrveMQNJDIE3xj7928vgi1cvkcY',
      process.env.JWT_2 ||
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.SOJx6R3v6ULcnQY-qrveMQNJDIE3xj7928vgi1cvkcY',
    ];
    return authorization[id];
  }

  public async getUser(filter: {}): Promise<User> {
    return DataBase.getUser(filter);
  }

  public getUserConfig(id: number): User {
    return this.users[id];
  }

  public getApiKey(): ApiKey {
    // Должны быть заданы переменные окружения
    if (!process.env.API_KEY || !process.env.API_SECRET || !process.env.COMPANY_ID || !process.env.OBJ_ID_KEY) {
      warn('Missing environment variables for API key configuration.');
    }

    return {
      key: process.env.API_KEY || 'error',
      secret: process.env.API_SECRET || 'error',
      companies: [{ id: this.getCompanyId(process.env.COMPANY_ID) }],
      id: process.env.OBJ_ID_KEY || 'error',
      active_config: !!process.env.ACTIVE_CONFIG || false,
      title: process.env.KEY_TITLE ? process.env.KEY_TITLE : 'user5',
    };

    // Резервные данные для обратной совместимости
    // return {
    //   key: process.env.API_KEY || '119155', // '80146' || '60984'
    //   secret: process.env.API_SECRET || 'Wz1nNW8a51H1KZrDUBK3MOriRfPmswMjkXlO7CbwD6MiULBO8q', // 'iFMfsyXVzREVH69iKbXOgt1rzj0S623PfAImLrgMXnXLigefsM' || '3zk9IAPG7elW728ujhzCcEGqMxYfbrrzVF7igpi15TBvitUNeS'
    //   companies: [{ id: this.getCompanyId(process.env.COMPANY_ID) || 'i738314881' }], // || 'i128563840'
    //   id: process.env.OBJ_ID_KEY || '118338', // '79344',
    //   active_config: !!process.env.ACTIVE_CONFIG || false, //if true, take data from getApiKey or getUserConfig, if false, take data from the database
    //   title: 'user5',
    // };
  }

  public getApiKeySuper(): ApiKey {
    return {
      key: process.env.API_KEY_SUPER || '27640',
      secret: process.env.API_SECRET_SUPER || '4n02KSSMHgJshMJjwWIvuI5TzOUIi5HLg1ZlknjL4cBjhnBHlk',
      companies: [{ id: '' }],
      id: '11028',
      active_config: !!process.env.ACTIVE_CONFIG || false, //if true, take data from getApiKey or getUserConfig, if false, take data from the database
    };
  }

  async getCorezoidConfig(): Promise<ApplicationConfig> {
    try {
      const response = await axios({
        method: 'GET',
        url: `${this.getApiUrl()}system/conf`,
      });

      account_host = response.data.web_settings.host.single_account;
      ws_host = response.data.web_settings.host.ws;

      this.config = response.data as ApplicationConfig;
      return this.config;
    } catch (err) {
      error('Error system conf', err);

      throw new Error('Не удалось получить конфигурацию');
    }
  }
}
