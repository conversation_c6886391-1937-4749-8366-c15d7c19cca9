{"husky": {"hooks": {"pre-commit": "npm run lint"}}, "name": "corezoid-automation-playwright", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"s3": "node infrastructure/runner/utils/awsClient.js", "lint": "npx eslint --ext .ts .", "test:api:all": "npx jest -i -c jest.api.config.js tests/api/ ", "test:api:story": "npx jest -i -c jest.universal.new.config.js tests/api/corezoid-user-story ", "test:api:limits": "npx jest -i -c jest.api.config.js tests/api/acces-management ", "test:api:config": "npx jest -i -c jest.api.config.js tests/api/corezoid_config ", "test:api:v2:part1": "npm run test:api:v2:part1:Stable && npm run test:api:v2:part1:Unstable", "test:api:v2:part1:Stable": "npx jest -c jest.universal.new.config.js tests/api/corezoid-api/v2/part-one/JSON --maxWorkers=100%", "test:api:v2:part1:Unstable": "npx jest -i -c jest.universal.new.config.js tests/api/corezoid-api/v2/part-one/Unstable/convs/convs.positive.test.ts tests/api/corezoid-api/v2/part-one/Unstable/nodes/nodes.positive.test.ts tests/api/corezoid-api/v2/part-one/Unstable/company_users/companyUsers.positiveCompany.test.ts", "test:api:v2:part2": "npm run test:api:v2:part2:Stable && npm run test:api:v2:part2:Unstable", "test:api:v2:part2:Stable": "npx jest -c jest.universal.new.config.js tests/api/corezoid-api/v2/part-two/Stable", "test:api:v2:part2:Unstable": "npx jest -i -c jest.universal.new.config.js tests/api/corezoid-api/v2/part-two/Unstable", "test:api:load": "k6 run tests/Load/load.js", "test:api:multitenant": "npx jest -i -c jest.api.config.js /tests/api-multitenant ", "test:api:universal": "npx jest -c jest.universal.new.config.js tests/api/Universal-test-suite ", "test:api:universalPROD": "npx jest -i -c jest.universal.config.js tests/api/Universal-test-suite --testPathIgnorePatterns=StopUpload.test.ts", "test:api:universal:withoutcompany": "npx jest -i -c jest.api.config.js tests/api/Universal-test-suite/ApiCall.test.ts tests/api/Universal-test-suite/ApiDinamicConstr.test.ts tests/api/Universal-test-suite/ApiKeyGroup.test.ts tests/api/Universal-test-suite/Code.test.ts tests/api/Universal-test-suite/Condition.test.ts tests/api/Universal-test-suite/Copy.test.ts tests/api/Universal-test-suite/Debag.test.ts tests/api/Universal-test-suite/Delay.test.ts tests/api/Universal-test-suite/LogicCallbackApi2Modify.test.ts tests/api/Universal-test-suite/LogicCallbackCallbackUrlMandrill.test.ts tests/api/Universal-test-suite/ModifyTask.test.ts tests/api/Universal-test-suite/MultAndSearch.test.ts tests/api/Universal-test-suite/MultAndSearch.test.ts tests/api/Universal-test-suite/QueueGetFromQueue.test.ts tests/api/Universal-test-suite/ReplyCallProcess.test.ts tests/api/Universal-test-suite/SetParameter.test.ts tests/api/Universal-test-suite/SumSetParameter.test.ts tests/api/Universal-test-suite/SyncApi.test.ts tests/api/Universal-test-suite/TaskParameter.test.ts tests/api/Universal-test-suite/FolderList.test.ts ", "test:api:db-call": "npx jest -i -c jest.api.config.js tests/api/corezoid-api/v2/JSON/instance tests/api/corezoid-user-story/db-call ", "test:api:universal:desktop": "npx jest -i -c jest.api.config.js tests/api/Universal-test-suite ", "test:api:clear": "npx jest -i -c jest.api.config.js tests/api/deleteObj.test.ts --testTimeout=1200000", "test:web:test": "npx jest --maxWorkers=1 -c jest.web.config.js tests/api/corezoid-api/v2/JSON/conv_params/conv_params.positive.test.ts", "test:api:apigw": "npx jest -i -c jest.api.config.js tests/api/apiGW ", "test:api:apigwCorezoid": "npx jest -i -c jest.api.config.js tests/api/apiGW/ApiGWCorezoid.positive.test.ts ", "test:api:git-call": "npx jest -i -c jest.universal.new.config.js tests/api/git_call/git-call.story.test.ts ", "test:unstable": "npx jest -c jest.universal.new.config.js tests/api/Universal-test-suite/StopUpload.test.ts"}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"@types/faker": "^5.5.6", "@types/jest": "26.0.23", "@types/k6": "^0.35.2", "axios": "0.21.4", "crypto-js": "4.0.0", "dotenv": "^16.3.1", "jest-circus": "27.0.1", "k6": "^0.0.0", "native-dns": "^0.7.0", "playwright": "^1.35.1", "playwright-video": "2.4.0", "puppeteer": "2.1.1", "qs": "^6.11.0", "sha1": "^1.1.1", "ts-jest": "27.0.1", "ts-node": "10.0.0", "typescript": "4.3.2", "ws": "^8.18.0", "yarn": "1.22.18"}, "devDependencies": {"@babel/core": "7.23.3", "@babel/preset-env": "7.23.3", "@babel/preset-typescript": "7.23.3", "@ffmpeg-installer/ffmpeg": "1.0.20", "@playwright/test": "^1.36.0", "@types/crypto-js": "3.1.47", "@types/js-yaml": "3.12.5", "@types/mongodb": "^3.6.3", "@types/qs": "^6.9.7", "@types/set-cookie-parser": "0.0.6", "@types/socket.io": "^2.1.11", "@types/socket.io-client": "^1.4.34", "@types/uuid": "^8.0.0", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "adm-zip": "^0.4.16", "ajv": "8.0.5", "aws-sdk": "2.714.0", "babel-jest": "29.7.0", "clipboardy": "^2.3.0", "crypto-js": "4.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-jest": "^22.16.0", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-no-comments": "^1.1.10", "eslint-plugin-prettier": "^3.1.2", "faker": "^5.5.3", "form-data": "^3.0.0", "husky": "^4.2.5", "jest": "^27.5.1", "jest-extended": "^0.11.5", "jest-html-reporters": "3.0.11", "jest-junit": "^12.3.0", "js-yaml": "3.14.0", "lint-staged": "10.2.6", "mongodb": "^3.6.3", "prettier": "^1.19.1", "set-cookie-parser": "2.4.6", "uuid": "^8.2.0", "winston": "^3.3.3"}}