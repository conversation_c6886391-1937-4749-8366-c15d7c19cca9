# WebSocket Monitor Script

## Описание
Скрипт для подключения к WebSocket и мониторинга всех процессов из файла `dataConvCode.json`.

## Использование

1. **Обновите конфигурацию** в файле `websocket-monitor.js`:
   ```javascript
   const COOKIE = 'your_cookie_here'; // Замените на актуальную cookie
   const COMPANY_ID = 12345; // Замените на актуальный company_id  
   const USER_ID = 67890; // Замените на актуальный user_id
   ```

2. **Запустите скрипт**:
   ```bash
   node scripts/websocket-monitor.js
   ```

## Что делает скрипт

1. **Подключается к WebSocket** `wss://pre.corezoid.com/ws`
2. **Отправляет мониторинговые запросы** для каждого процесса (conv_id) из `dataConvCode.json`
3. **Мониторит 3 типа событий**:
   - `monitor_stat/graph_tacts`
   - `monitor_list/conv`
   - `monitor_stat/graph_node`
4. **Выводит в консоль**:
   - Статус подключения
   - Отправленные сообщения
   - Полученные ответы
   - Статистику запросов

## Пример вывода

```
🎯 WebSocket Process Monitor
============================

Connecting to WebSocket: wss://pre.corezoid.com/ws
✅ WebSocket connected successfully

🚀 Starting monitoring for 20 processes
📋 Processes: 2509697, 2509698, 2509699, ...
🔗 Endpoints: monitor_stat/graph_tacts, monitor_list/conv, monitor_stat/graph_node

📊 Processing conv_id: 2509697
📤 Sending monitoring request for conv_id: 2509697, endpoint: monitor_stat/graph_tacts
   Message: {
     "ops": [
       {
         "type": "monitor_stat",
         "obj": "graph_tacts", 
         "conv_id": 2509697,
         "company_id": 12345,
         "user_id": 67890
       }
     ]
   }
✅ Request sent successfully

📨 Message #1 received: {
  "ops": [
    {
      "request_proc": "ok",
      "type": "monitor_stat",
      "obj": "graph_tacts",
      "data": {...}
    }
  ]
}
```

## Получение параметров

Для получения актуальных значений:
- **COOKIE**: из браузера (Developer Tools → Network → любой запрос → Headers)
- **COMPANY_ID**: из API ключа или браузера
- **USER_ID**: из профиля пользователя

## Остановка скрипта

Нажмите `Ctrl+C` для корректного завершения работы скрипта.