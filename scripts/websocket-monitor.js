const WebSocket = require('ws');
const dataConvCode = require('../tests/K6/data/dataConvCode.json');

// Configuration - update these values as needed
const WS_URL = 'wss://pre.corezoid.com';
const WS_PATH = '/ws';
const COOKIE = 'your_cookie_here'; // Replace with actual cookie
const ORIGIN = 'https://pre.corezoid.com';
const COMPANY_ID = 12345; // Replace with actual company_id
const USER_ID = 67890; // Replace with actual user_id

const monitoringEndpoints = [
  { type: 'monitor_stat', obj: 'graph_tacts' },
  { type: 'monitor_list', obj: 'conv' },
  { type: 'monitor_stat', obj: 'graph_node' }
];

class WebSocketMonitor {
  constructor() {
    this.ws = null;
    this.connected = false;
    this.messageCount = 0;
  }

  connect() {
    return new Promise((resolve, reject) => {
      console.log(`Connecting to WebSocket: ${WS_URL}${WS_PATH}`);
      
      this.ws = new WebSocket(`${WS_URL}${WS_PATH}`, {
        headers: {
          'Cookie': COOKIE,
          'Origin': ORIGIN
        }
      });

      this.ws.on('open', () => {
        console.log('✅ WebSocket connected successfully');
        this.connected = true;
        resolve();
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error.message);
        reject(error);
      });

      this.ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
        this.connected = false;
      });

      this.ws.on('message', (data) => {
        this.messageCount++;
        try {
          const message = JSON.parse(data.toString());
          console.log(`📨 Message #${this.messageCount} received:`, JSON.stringify(message, null, 2));
        } catch (err) {
          console.log(`📨 Raw message #${this.messageCount}:`, data.toString());
        }
      });
    });
  }

  sendMonitoringRequest(conv_id, endpoint) {
    return new Promise((resolve, reject) => {
      if (!this.connected) {
        reject(new Error('WebSocket not connected'));
        return;
      }

      const message = {
        ops: [
          {
            type: endpoint.type,
            obj: endpoint.obj,
            conv_id: conv_id,
            node_id: endpoint.obj === 'graph_node' ? '6866345eae906a62ac01cce8' : undefined,
            company_id: COMPANY_ID,
            user_id: USER_ID,
            status: 'on'
          }
        ]
      };

      // Remove undefined fields
      if (message.ops[0].node_id === undefined) {
        delete message.ops[0].node_id;
      }

      console.log(`📤 Sending monitoring request for conv_id: ${conv_id}, endpoint: ${endpoint.type}/${endpoint.obj}`);
      console.log(`   Message:`, JSON.stringify(message, null, 2));

      try {
        this.ws.send(JSON.stringify(message));
        console.log(`✅ Request sent successfully`);
        resolve();
      } catch (err) {
        console.error(`❌ Failed to send request:`, err.message);
        reject(err);
      }
    });
  }

  async monitorAllProcesses() {
    console.log(`\n🚀 Starting monitoring for ${dataConvCode.length} processes`);
    console.log(`📋 Processes: ${dataConvCode.map(p => p.conv_id).join(', ')}`);
    console.log(`🔗 Endpoints: ${monitoringEndpoints.map(e => `${e.type}/${e.obj}`).join(', ')}\n`);

    let totalRequests = 0;
    let successfulRequests = 0;

    for (const process of dataConvCode) {
      console.log(`\n📊 Processing conv_id: ${process.conv_id}`);
      
      for (const endpoint of monitoringEndpoints) {
        totalRequests++;
        try {
          await this.sendMonitoringRequest(process.conv_id, endpoint);
          successfulRequests++;
          
          // Wait a bit between requests
          await new Promise(resolve => setTimeout(resolve, 200));
        } catch (err) {
          console.error(`❌ Failed request for conv_id: ${process.conv_id}, endpoint: ${endpoint.type}/${endpoint.obj}`, err.message);
        }
      }
    }

    console.log(`\n📈 Summary:`);
    console.log(`   Total requests: ${totalRequests}`);
    console.log(`   Successful: ${successfulRequests}`);
    console.log(`   Failed: ${totalRequests - successfulRequests}`);
    console.log(`   Success rate: ${((successfulRequests / totalRequests) * 100).toFixed(1)}%`);
  }

  close() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

async function main() {
  console.log('🎯 WebSocket Process Monitor');
  console.log('============================\n');

  const monitor = new WebSocketMonitor();

  try {
    await monitor.connect();
    
    // Wait a moment for connection to stabilize
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await monitor.monitorAllProcesses();
    
    // Keep connection open for a while to receive responses
    console.log('\n⏳ Keeping connection open for 30 seconds to receive responses...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('💥 Error during monitoring:', error.message);
  } finally {
    monitor.close();
    console.log('\n👋 Monitoring session ended');
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Received interrupt signal, closing connection...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received terminate signal, closing connection...');
  process.exit(0);
});

main().catch(console.error);