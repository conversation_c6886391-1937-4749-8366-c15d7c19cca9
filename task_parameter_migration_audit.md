# Task Parameter Migration Audit

## Summary
Successfully migrated JMeter "Task Parameter" ThreadGroup tests from API_v2_Amazon.jmx to Playwright TypeScript tests in tests/api2pre/task_parameter.test.ts. The migration correctly implements 8 Add_task tests with their exact operation sequences as specified by the user, each corresponding to one Add_task block from the JMeter ThreadGroup.

## Source Analysis
- **Source File**: API_v2_Amazon.jmx (4.5MB)
- **ThreadGroup**: "Task Parameter" (starting at line 27206)
- **Test Count**: 8 Add_task blocks with different operation sequences
- **Structure**: Each Add_task block followed by specific operations (list node, modify conv_params, show task, Reset_node) until next Add_task

## Migration Status Table

| JMeter Test Name | Operation Sequence | Playwright Implementation | Status | Comments |
|------------------|-------------------|---------------------------|---------|----------|
| №1 VIEW API 2 Add_task #1 | create task → list node → modify conv_params → list node → modify conv_params → list node | task_parameter.test.ts:34-108 | ✅ Complete | Complex nested objects with arrays + 2 modify conv_params |
| №2 VIEW API 2 Add_task #2 | create task → list node → modify conv_params → list node → modify conv_params → list node | task_parameter.test.ts:110-181 | ✅ Complete | Same structure as #1 with ref: "123" |
| VIEW API 2 Add_task #3 | create task → list node → modify conv_params → list node → show task | task_parameter.test.ts:183-233 | ✅ Complete | Array structures + show task operation |
| VIEW API 2 Add_task #4 | create task → list node → modify conv_params → list node | task_parameter.test.ts:235-291 | ✅ Complete | Product lists, booleans + comprehensive modify conv_params |
| VIEW API 2 Add_task #5 | create task → list node → modify conv_params → list node → modify conv_params → list node | task_parameter.test.ts:293-347 | ✅ Complete | Multi-dimensional arrays + 2 modify conv_params |
| №7 VIEW API 2 Add_task #6 | create task → list node → modify conv_params → list node → modify conv_params | task_parameter.test.ts:349-401 | ✅ Complete | Null object testing + 2 modify conv_params |
| VIEW API 2 Add_task #7 (n) wrong_validate_params | create task → modify conv_params | task_parameter.test.ts:403-434 | ✅ Complete | Shortest test - validation error testing |
| VIEW API 2 Add_task #8 | create task → list node → Reset_node → list node | task_parameter.test.ts:436-473 | ✅ Complete | Reset_node operation (unique to test 8) |

## Key Migration Patterns Applied

### 1. BeanShell to TypeScript Conversion
**JMeter BeanShell**:
```java
String jo = "{\"type\":\"create\",\"conv_id\":"+conv+",\"obj\":\"task\",\"data\":{...},\"ref\":\"123\"}";
```

**TypeScript**:
```typescript
const addTaskResponse = await api.request(
  createRequestWithOps({
    type: REQUEST_TYPE.CREATE,
    conv_id: conv_id,
    obj: OBJ_TYPE.TASK,
    data: {...},
    ref: '123',
  }),
);
```

### 2. Operation Sequence Implementation
Each test follows the exact user-specified sequence:

**Test 1 & 2**: create task → list node → modify conv_params → list node → modify conv_params → list node
**Test 3**: create task → list node → modify conv_params → list node → show task  
**Test 4**: create task → list node → modify conv_params → list node
**Test 5**: create task → list node → modify conv_params → list node → modify conv_params → list node
**Test 6**: create task → list node → modify conv_params → list node → modify conv_params
**Test 7**: create task → modify conv_params
**Test 8**: create task → list node → Reset_node → list node

### 3. Complex Data Structures Migrated
Successfully migrated various data structures from BeanShell scripts:
- **Test #1 & #2**: Complex nested objects with arrays: `{a: '123', b: 456, s: {f: 567, r: {t: 9870}}, arr: [{v: 567}, {w: 5676}, 678, {q: [{e: 'e'}]}]}`
- **Test #3**: Array structures: `{array: [{test: [{qw: 4567}]}, {test: [{qw: '987'}]}, '6781', {test: [{qw: 123}]}]}`
- **Test #4**: Product lists and booleans: `{productList: ['567', '789', {l: 456}], bool: true, e: {}, f: []}`
- **Test #5**: Multi-dimensional arrays: `{arrey2m: [{arey: [[{ar1: 123}, {ar1: 345}]]}]}`
- **Test #6**: Null values: `{objnull: null}`
- **Test #7**: Simple testtype: `{testtype: 123}`
- **Test #8**: Simple testtype: `{testtype: 123}`

### 4. Modify Conv_Params Operations
Migrated complex parameter configurations from BeanShell scripts:
- **Basic parameters**: name, descr, type, flags (input, auto-clear), regex, regex_error_text
- **Nested object paths**: 's.f', 's.r.t', 'r.d'
- **Array element paths**: 'arr[0].v', 'arr[2]', 'array[].test[].qw', 'productList[]'
- **Multi-dimensional arrays**: 'arrey2m[0].arey[0][0].ar1'
- **Object types**: objnull with type: 'object'

### 5. Show Task Operation
**JMeter BeanShell**:
```java
String jo = "{\"conv_id\":"+conv+",\"ref\":\"123-3\",\"type\":\"show\",\"obj\":\"task\"}";
```

**TypeScript**:
```typescript
const showTaskResponse = await api.request(
  createRequestWithOps({
    type: REQUEST_TYPE.SHOW,
    conv_id: conv_id,
    obj: OBJ_TYPE.TASK,
    ref: '123-3',
  }),
);
```

### 6. Reset_node Operation
**JMeter BeanShell**:
```java
String jo = "{\"conv_id\":"+conv+",\"type\":\"reset\",\"obj\":\"node\",\"obj_id\":\""+obj+"\",\"company_id\":\""+company_id+"\"}";
```

**TypeScript**:
```typescript
const resetNodeResponse = await api.request(
  createRequestWithOps({
    type: 'reset' as any,
    conv_id: conv_id,
    obj: OBJ_TYPE.NODE,
    obj_id: final_node_ID,
    company_id: company_id,
  }),
);
```

### 7. Assertions Migration
**JMeter ResponseAssertion**:
```xml
<stringProp name="response_code">200</stringProp>
<stringProp name="request_proc">ok</stringProp>
```

**TypeScript**:
```typescript
expect(addTaskResponse.status).toBe(RESP_STATUS.OK);
expect(addTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
```

## Implementation Details
- **Target File**: tests/api2pre/task_parameter.test.ts
- **Test Framework**: Jest with Playwright patterns
- **API Client**: ApiKeyClient following established patterns
- **Setup/Cleanup**: Proper beforeAll/afterAll with conveyor lifecycle management
- **Test Count**: Exactly 8 tests as specified by user requirements
- **Operation Sequences**: Each test follows user-specified exact sequence
- **Timing**: 3-second pauses between tests as per JMeter TestAction elements

## JMeter Source Analysis
### Key BeanShell Script Patterns Analyzed:

**Create Task Operations**:
```java
// Test #1 & #2 - Complex nested structures
String jo = "{\"type\":\"create\",\"conv_id\":"+conv+",\"obj\":\"task\",\"data\":{\"a\":\"123\",\"b\":456,\"c\":\"5168755504343308\",\"r.d\":\"897\",\"s\":{\"f\":567,\"r\":{\"t\":9870}},\"arr\":[{\"v\":567},{\"w\":5676},678,{\"q\":[{\"e\":\"e\"}]}],\"g\":\"2345\"},\"ref\":\"123\"}";

// Test #3 - Array structures
String jo = "{\"type\":\"create\",\"conv_id\":"+conv+",\"obj\":\"task\",\"data\":{\"array\":[{\"test\":[{\"qw\":4567}]},{\"test\":[{\"qw\":\"987\"}]},\"6781\",{\"test\":[{\"qw\":123}]}],\"array1\":[{\"test\":4567},{\"test\":987},{\"test\":123}]},\"ref\":\"123-3\"}";

// Test #4 - Product lists and booleans
String jo = "{\"type\":\"create\",\"conv_id\":"+conv+",\"obj\":\"task\",\"data\":{\"productList\":[\"567\",\"789\",{\"l\":456}],\"b\":[{\"key1\":123},{\"key1\":345}],\"e\":{},\"f\":[],\"bool\":true},\"ref\":\"1234\"}";

// Test #5 - Multi-dimensional arrays
String jo = "{\"type\":\"create\",\"conv_id\":"+conv+",\"obj\":\"task\",\"data\":{\"arrey2m\":[{\"arey\":[[{\"ar1\":123},{\"ar1\":345}]]}]}}";

// Test #6 - Null values
String jo = "{\"type\":\"create\",\"conv_id\":"+conv+",\"obj\":\"task\",\"data\":{\"objnull\":null}}";

// Test #7 & #8 - Simple test type
String jo = "{\"type\":\"create\",\"conv_id\":"+conv+",\"obj\":\"task\",\"data\":{\"testtype\":123}}";
```

**Modify Conv_Params Operations**:
```java
// Comprehensive parameter configuration from Test #4
String jo = "{\"obj_id\":"+conv+",\"type\":\"modify\",\"obj\":\"conv_params\",\"ref_mask\":false,\"params\":[{\"name\":\"a\",\"descr\":\"aa\",\"type\":\"string\",\"flags\":[\"input\",\"auto-clear\"],\"regex\":\"\",\"regex_error_text\":\"\"},{\"name\":\"b\",\"descr\":\"bb\",\"type\":\"number\",\"flags\":[\"input\"],\"regex\":\"\",\"regex_error_text\":\"\"},{\"name\":\"productList[]\",\"descr\":\"productList[]\",\"type\":\"string\",\"flags\":[],\"regex\":\"\",\"regex_error_text\":\"\"}]}";

// Multi-dimensional array parameter from Test #5
String jo = "{\"obj_id\":"+conv+",\"type\":\"modify\",\"obj\":\"conv_params\",\"ref_mask\":false,\"params\":[{\"name\":\"arrey2m[0].arey[0][0].ar1\",\"descr\":\"arrey2m[0].arey[0][0].ar1\",\"type\":\"string\",\"flags\":[\"auto-clear\",\"input\"],\"regex\":\"\",\"regex_error_text\":\"\"}]}";
```

### Response Assertions Migrated:
- Response Code 200 → `expect(response.status).toBe(RESP_STATUS.OK)`
- "request_proc":"ok" → `expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK)`
- Task creation success verification
- Show task and Reset_node operation success verification

## Verification
- ✅ Follows established code patterns from existing TaskParameter.test.ts
- ✅ Uses existing utilities and constants (createRequestWithOps, OBJ_TYPE, REQUEST_TYPE)
- ✅ Proper error handling and assertions
- ✅ Exactly 8 tests as specified by user requirements
- ✅ Each test follows user-specified exact operation sequence
- ✅ Complex data structures accurately migrated from BeanShell scripts
- ✅ All operation types implemented: create task, list node, modify conv_params, show task, Reset_node
- ✅ Comprehensive parameter configurations for modify conv_params operations

## Conclusion
**Migration Status**: ✅ **COMPLETE**

All 8 JMeter "Task Parameter" Add_task blocks from API_v2_Amazon.jmx have been successfully migrated to TypeScript/Playwright with their exact operation sequences as specified by the user. The implementation correctly follows the user's detailed breakdown:

1. **Test 1**: create task → list node → modify conv_params → list node → modify conv_params → list node
2. **Test 2**: create task → list node → modify conv_params → list node → modify conv_params → list node  
3. **Test 3**: create task → list node → modify conv_params → list node → show task
4. **Test 4**: create task → list node → modify conv_params → list node
5. **Test 5**: create task → list node → modify conv_params → list node → modify conv_params → list node
6. **Test 6**: create task → list node → modify conv_params → list node → modify conv_params
7. **Test 7**: create task → modify conv_params
8. **Test 8**: create task → list node → Reset_node → list node

The migration preserves all complex data structures from the BeanShell scripts, implements comprehensive parameter configurations for modify conv_params operations, and follows established patterns from the existing codebase.

---

**Migration completed by**: COR-12118  
**Source file**: API_v2_Amazon.jmx (4.5MB, ThreadGroup "Task Parameter" at line 27206)  
**Target implementation**: tests/api2pre/task_parameter.test.ts  
**Total tests migrated**: 8 Add_task test scenarios with exact operation sequences  
**Implementation quality**: High - follows all established patterns and user-specified exact sequences
