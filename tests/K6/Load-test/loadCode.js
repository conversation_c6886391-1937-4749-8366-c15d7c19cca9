import http from 'k6/http';
import { check, group } from 'k6';
import { SharedArray } from 'k6/data';
import { randomItem } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';
import { sendResultsToWebhook } from '../utils/sendResult.js';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/2.4.0/dist/bundle.js';
import EnvConfig from './config/envconfig.js';
import { generateUrlWithSignature } from '../utils/generateSignature.js';

const config = EnvConfig.pre;
const host = config.BASE_URL;
const API_LOGIN = config.API_LOGIN;
const API_SECRET = config.API_SECRET;
const params = EnvConfig.params;

export const options = {
  scenarios: {
    my_api_test: {
      executor: 'ramping-arrival-rate',

      startRate: 1,
      timeUnit: '5s',      // Каждые 5 секунд
      stages: [
        { target: 2, duration: '15s' },  // Разогрев: 2 запроса за 5с в течение 15с
        { target: 4, duration: '30s' },  // Основная нагрузка: 4 запроса за 5с в течение 30с  
        { target: 6, duration: '30s' },  // Пиковая нагрузка: 6 запросов за 5с в течение 30с
        { target: 2, duration: '15s' },  // Остывание: 2 запроса за 5с в течение 15с
      ],
      preAllocatedVUs: 10,
      maxVUs: 100,         // Снизили для стабильности
    },
  },
};

const dataTask = new SharedArray('some data name', function() {
  return JSON.parse(open('../data/dataTask1KB.json'));
});

const dataTaskID = new SharedArray('conv_id', function() {
  return JSON.parse(open(`../data/dataConvCode.json`));
});

const dataConv = dataTaskID.map(data => {
  return data.conv_id;
});

export default function() {
  const convID = randomItem(dataConv);
  const GMT_UNIXTIME = Math.floor(Date.now() / 1000);

  group('Load code', function() {
    const payload = dataTask[0];
    const payload2 = JSON.stringify({
      ops: [
        {
          conv_id: convID,
          type: 'create',
          obj: 'task',
          action: 'user',
          ref: `${GMT_UNIXTIME}`,
          data: payload,
        },
      ],
    });

    const url = generateUrlWithSignature(payload2, API_LOGIN, API_SECRET, host);
    const response = http.post(url, payload2, params);
    const body = JSON.parse(JSON.stringify(response.body, null, 2));
    console.log(body);

    check(response, {
      'is status 200': r => r.status === 200,
      proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
    });
  });
}

export function handleSummary(data) {
  sendResultsToWebhook(data);
  return {
    'report/K6/summary.html': htmlReport(data),
  };
}
