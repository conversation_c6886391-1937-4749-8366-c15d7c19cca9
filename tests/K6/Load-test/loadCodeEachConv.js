import http from 'k6/http';
import { check, group } from 'k6';
import { SharedArray } from 'k6/data';
import { sendResultsToWebhook } from '../utils/sendResult.js';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/2.4.0/dist/bundle.js';
import EnvConfig from './config/envconfig.js';
import { generateUrlWithSignature } from '../utils/generateSignature.js';

const config = EnvConfig.pre;
const host = config.BASE_URL;
const API_LOGIN = config.API_LOGIN;
const API_SECRET = config.API_SECRET;
const params = EnvConfig.params;

const dataTask = new SharedArray('some data name', function() {
  return JSON.parse(open('../data/dataTask1KB.json'));
});

const dataTaskID = new SharedArray('conv_id', function() {
  return JSON.parse(open(`../data/dataConvCode.json`));
});

const dataConv = dataTaskID.map(data => {
  return data.conv_id;
});

// Создаем отдельные сценарии для каждого процесса с фиксированным временем
const scenarios = {};
dataConv.forEach(convId => {
  scenarios[`conv_${convId}`] = {
    executor: 'constant-arrival-rate',
    rate: 1, // 1 запрос в секунду для каждого процесса
    timeUnit: '1s',
    duration: '60s',
    preAllocatedVUs: 2,
    maxVUs: 3,
    env: { CONV_ID: convId.toString() }, // Передаем conv_id через переменную окружения
  };
});

export const options = {
  scenarios,
};

console.log(`🚀 Configured load test for ${dataConv.length} processes:`, dataConv);

// Главная функция - использует переменную окружения для определения conv_id
export default function() {
  // Получаем conv_id из переменной окружения сценария
  const convId = __ENV.CONV_ID ? parseInt(__ENV.CONV_ID) : dataConv[0];

  const GMT_UNIXTIME = Math.floor(Date.now() / 1000);

  group(`Load conv_${convId}`, function() {
    const payload = dataTask[0];
    const payload2 = JSON.stringify({
      ops: [
        {
          conv_id: convId,
          type: 'create',
          obj: 'task',
          action: 'user',
          ref: `${GMT_UNIXTIME}_${Math.random()
            .toString(36)
            .substring(2, 11)}`,
          data: payload,
        },
      ],
    });

    const url = generateUrlWithSignature(payload2, API_LOGIN, API_SECRET, host);
    const response = http.post(url, payload2, params);

    // Детальное логирование запросов
    const timestamp = new Date().toISOString();
    const requestDetails = {
      timestamp,
      conv_id: convId,
      request: {
        url: url.split('?')[0], // URL без query parameters для безопасности
        method: 'POST',
        payload: JSON.parse(payload2),
        headers: params.headers,
      },
      response: {
        status: response.status,
        duration: response.timings.duration,
        body: JSON.parse(response.body),
        headers: response.headers,
      },
    };

    // Проверка успешности через parsed JSON
    const parsedResponse = JSON.parse(response.body);
    const isSuccess = parsedResponse.ops && parsedResponse.ops[0] && parsedResponse.ops[0].proc === 'ok';

    // Компактный лог в консоль
    console.log(
      `📦 Conv ${convId}: ${response.status} - ${isSuccess ? 'OK' : 'FAIL'} (${response.timings.duration}ms)`,
    );

    // Детальный лог в stderr для записи в файл
    console.error(`REQUEST_LOG: ${JSON.stringify(requestDetails)}`);

    check(response, {
      'is status 200': r => r.status === 200,
      proc_ok: () => isSuccess,
    });
  });
}

export function handleSummary(data) {
  sendResultsToWebhook(data);
  return {
    'report/K6/summary.html': htmlReport(data),
  };
}
