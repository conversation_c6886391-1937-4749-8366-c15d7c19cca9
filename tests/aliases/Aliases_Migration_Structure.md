# 📋 Структура тестов Aliases.jmx для миграции

## 📊 Общая статистика

- **J<PERSON> файл:** `Aliases.jmx` (4.7MB, 71,070 строк)
- **HTTP запросов:** 698
- **BeanShell скриптов:** 1,362
- **Response Assertions:** 4,926
- **ThreadGroups:** 15 (11 активных)

## 🎯 Основные группы тестов

### 1. **Базовые CRUD операции** (168 тестов)
#### Простые тесты (2-3 дня)

```typescript
describe('Basic CRUD Operations', () => {
  test('List_Aliases', async () => {
    // {"type":"list","obj":"aliases","company_id":"...","project_id":0}
    // Ожидается: "proc":"ok", "list" array
  });

  test('Show_Aliases', async () => {
    // {"type":"show","obj":"alias","obj_id":"...","company_id":"..."}
    // Ожидается: "proc":"ok", "show" object с деталями алиаса
  });

  test('Create_Alias', async () => {
    // {"type":"create","obj":"alias","title":"...","short_name":"...","company_id":"..."}
    // Ожидается: "proc":"ok", "obj_id" нового алиаса
  });

  test('Modify_Alias', async () => {
    // {"type":"modify","obj":"alias","obj_id":"...","title":"...","company_id":"..."}
    // Ожидается: "proc":"ok"
  });

  test('Delete_Alias', async () => {
    // {"type":"delete","obj":"alias","obj_id":"...","company_id":"..."}
    // Ожидается: "proc":"ok"
  });
});
```

### 2. **List операции с параметрами** (105 тестов)
#### Простые тесты (1-2 дня)

```typescript
describe('List Operations with Parameters', () => {
  test('List_Aliases_pattern', async () => {
    // {"type":"list","obj":"aliases","company_id":"...","pattern":"search_term"}
  });

  test('List_Aliases_sort_name', async () => {
    // {"type":"list","obj":"aliases","company_id":"...","sort":"name","order":"asc"}
  });

  test('List_Aliases_sort_short_name', async () => {
    // {"type":"list","obj":"aliases","company_id":"...","sort":"short_name","order":"desc"}
  });

  test('List_Aliases_sort_owner', async () => {
    // {"type":"list","obj":"aliases","company_id":"...","sort":"owner"}
  });

  test('List_Aliases_sort_date', async () => {
    // {"type":"list","obj":"aliases","company_id":"...","sort":"date"}
  });

  test('List_Aliases_sort_obj_to_id', async () => {
    // {"type":"list","obj":"aliases","company_id":"...","sort":"obj_to_id"}
  });
});
```

### 3. **Link операции** (74 теста)
#### Средние тесты (2-3 дня)

```typescript
describe('Link Operations', () => {
  test('Link_Alias_SD', async () => {
    // Создание State Diagram
    // {"type":"create","obj":"conv","title":"SD","conv_type":"state","company_id":"..."}
    // Связывание алиаса
    // {"type":"link","obj":"alias","obj_id":"...","obj_to_id":"...","obj_to_type":"conv"}
  });

  test('Download_conv_with_alias', async () => {
    // {"type":"get","obj":"obj_scheme","obj_id":"...","with_alias":true,"format":"zip"}
    // endpoint: /api/2/download/...
  });
});
```

### 4. **Callback Hash операции** (78 тестов)
#### Средние тесты (2-3 дня)

```typescript
describe('Callback Hash Operations', () => {
  test('Create_callback_hash', async () => {
    // {"type":"create","obj":"callback_hash","alias_id":"...","obj_type":"alias","project_id":0}
    // Ожидается: "callback_hash" значение
  });

  test('Get_callback_hash', async () => {
    // {"type":"get","obj":"callback_hash","alias_id":"...","obj_type":"alias","project_id":0}
  });

  test('Delete_callback_hash', async () => {
    // {"type":"delete","obj":"callback_hash","obj_id":"...","company_id":"..."}
  });
});
```

### 5. **Public API операции** (67 тестов)
#### Сложные тесты (3-4 дня)

```typescript
describe('Public API Operations', () => {
  test('Create_task_public_url_JSON', async () => {
    // POST /api/2/json/public/@{alias}/{project_id}/{stage_id}/{company_id}/{callback_hash}
    // Content-Type: application/json
    // {"a":"test_value"}
  });

  test('Create_task_public_url_XML', async () => {
    // POST /api/2/xml/public/@{alias}/{project_id}/{stage_id}/{company_id}/{callback_hash}
    // Content-Type: application/xml
    // <data><a>test_value</a></data>
  });

  test('Create_task_public_url_NVP', async () => {
    // POST /api/2/nvp/public/@{alias}/{project_id}/{stage_id}/{company_id}/{callback_hash}
    // Content-Type: application/x-www-form-urlencoded
    // a=test_value
  });
});
```

### 6. **Favorite операции** (10 тестов)
#### Простые тесты (0.5 дня)

```typescript
describe('Favorite Operations', () => {
  test('Favorite_Alias_true', async () => {
    // {"type":"favorite","obj":"alias","obj_id":"...","favorite":true,"company_id":"..."}
  });

  test('Favorite_Alias_false', async () => {
    // {"type":"favorite","obj":"alias","obj_id":"...","favorite":false,"company_id":"..."}
  });
});
```

### 7. **Upsert операции** (14 тестов)
#### Средние тесты (1 день)

```typescript
describe('Upsert Operations', () => {
  test('Upsert_Aliases', async () => {
    // {"type":"upsert","obj":"aliases","data":[{"short_name":"...","title":"..."}],"company_id":"..."}
    // Создание или обновление множественных алиасов
  });
});
```

### 8. **Права доступа** (45 тестов)
#### Сложные тесты (2-3 дня)

```typescript
describe('Access Control', () => {
  test('Access_denied_no_privs', async () => {
    // Тест без прав доступа
    // Ожидается: "proc":"error", error_msg о недостатке прав
  });

  test('Access_allowed_with_privs', async () => {
    // Предварительное предоставление прав
    // {"type":"link","obj":"alias","privs":[{"type":"view","list_obj":["all"]}]}
    // Затем доступ к алиасу
  });
});
```

### 9. **Project Integration** (89 тестов)
#### Сложные тесты (3-4 дня)

```typescript
describe('Project Integration', () => {
  test('Aliases_in_project', async () => {
    // Создание проекта
    // {"type":"create","obj":"project","title":"...","stages":[...]}
    // Создание алиаса в проекте
    // {"type":"create","obj":"alias","project_id":"...","stage_id":"..."}
  });

  test('Callback_hash_in_project', async () => {
    // Callback hash для алиасов в проектах
    // {"type":"create","obj":"callback_hash","alias_id":"...","project_id":"...","stage_id":"..."}
  });
});
```

### 10. **API Copy операции** (25 тестов)
#### Сложные тесты (2 дня)

```typescript
describe('API Copy Operations', () => {
  test('Api_copy_with_alias', async () => {
    // Настройка API Copy логики через алиас
    // {"type":"modify","obj":"node","logics":[{"type":"api_copy","conv_alias":"..."}]}
  });
});
```

### 11. **Queue операции** (22 теста)
#### Сложные тесты (1-2 дня)

```typescript
describe('Queue Operations', () => {
  test('Api_queue_with_alias', async () => {
    // {"type":"modify","obj":"node","logics":[{"type":"api_queue","conv_alias":"..."}]}
  });

  test('Api_get_task_with_alias', async () => {
    // {"type":"modify","obj":"node","logics":[{"type":"api_get_task","conv_alias":"..."}]}
  });
});
```

## 🔧 Технические детали

### Используемые утилиты проекта
```typescript
// Основная функция для API запросов
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { ApiKeyClient } from '../../../application/api/ApiKeyClient';

// Типичный запрос
const response = await api.request(
  createRequestWithOps({
    type: REQUEST_TYPE.LIST,
    obj: OBJ_TYPE.ALIAS,
    company_id,
    project_id: 0,
  }),
);
```

### Переменные в TypeScript тестах
```typescript
// Переменные из beforeAll
let company_id: string;
let project_id: string | number;
let stage_id: string | number;
let alias_id: string | number;
let callback_hash: string;
let short_name: string;

// Генерация уникальных имен
short_name = `alias${Date.now()}`;
```

### Response Assertions паттерны
```typescript
// Успешный ответ
expect(response.body.ops[0].proc).toEqual('ok');

// Список объектов
expect(response.body.ops[0].list).toBeDefined();
expect(response.body.ops[0].list.length).toBeGreaterThan(0);

// Детали объекта
expect(response.body.ops[0].show.obj_id).toEqual(aliasId);
expect(response.body.ops[0].show.short_name).toEqual(shortName);

// Ошибка
expect(response.body.ops[0].proc).toEqual('error');
expect(response.body.ops[0].error_msg).toContain('Access denied');
```

## 📅 План миграции

### Этап 1: Базовые операции (4-5 дней)
- [ ] List/Show/Create/Delete Aliases
- [ ] List с параметрами (sort, pattern, order)
- [ ] Favorite операции

### Этап 2: Callback Hash (2-3 дня)  
- [ ] Create/Get/Delete callback hash
- [ ] Callback hash для разных project_id

### Этап 3: Link операции (2-3 дня)
- [ ] Link Alias to SD
- [ ] Download conv with alias
- [ ] Upsert операции

### Этап 4: Public API (3-4 дня)
- [ ] JSON/XML/NVP endpoints
- [ ] Различные форматы данных
- [ ] Валидация публичных URL

### Этап 5: Access Control (2-3 дня)
- [ ] Права доступа
- [ ] Access denied сценарии
- [ ] User permissions

### Этап 6: Project Integration (3-4 дня)
- [ ] Алиасы в проектах
- [ ] Callback hash в проектах
- [ ] Stage operations

### Этап 7: Advanced операции (3-4 дня)
- [ ] API Copy через алиасы
- [ ] Queue операции
- [ ] Error handling

**Общее время:** 19-26 рабочих дней

## 🎯 Приоритизация

### Высокий приоритет (критически важные)
1. Базовые CRUD операции
2. List с параметрами  
3. Callback Hash основы
4. Public API (JSON)

### Средний приоритет
1. Link операции
2. Access Control
3. Project Integration
4. Favorite/Upsert

### Низкий приоритет  
1. API Copy/Queue (специфические кейсы)
2. XML/NVP форматы Public API
3. Сложные error handling сценарии

## 💡 Рекомендации

1. **Начать с простых тестов** - List/Show/Create для быстрого результата
2. **Использовать существующие утилиты** - createRequestWithOps, ApiKeyClient
3. **Группировать тесты** - по beforeAll логике как в Reading Task
4. **Тестировать инкрементально** - каждый этап отдельно
5. **Документировать особенности** - специфичные для Aliases паттерны

Эта структура обеспечивает систематический подход к миграции всех 698 тестовых сценариев из Aliases.jmx в TypeScript.