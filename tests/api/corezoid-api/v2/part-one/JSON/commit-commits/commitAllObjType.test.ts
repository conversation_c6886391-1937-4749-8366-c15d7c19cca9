import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import confirmCommitSchema from '../../../../schemas/v2/commit/confirmCommit.schema.json';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../../../../utils/corezoidRequest';
import { requestListConv } from '../../../../../../../application/api/ApiObj';
import { NODE_LOGIC_TYPE } from '../../../../../../../application/api/obj_types/node';

describe('Confirm commit (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let newProject: string | number;
  let stageDev: string | number;
  let stagePre: string | number;
  let folderInStage: string | number;
  let convInFolderOne: string | number;
  let convInFolderTwo: string | number;
  let convInStage: string | number;
  let convInOtherStage: string | number;
  let nodeIdProcOne: string;
  let finNodeIdProcOne: string;
  let nodeIdProcTwo: string;
  let finNodeIdProcTwo: string;
  let nodeIdProcThree: string;
  let finNodeIdProcThree: string;
  let nodeIdProcFour: string;
  let finNodeIdProcFour: string;

  // Helper functions
  const modifyNode = async (
    obj_id: string,
    conv_id: string | number,
    to_node_id: string,
    logics: any,
    version: number,
  ): Promise<any> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [logics, { to_node_id, format: 'json', type: 'go', node_title: 'final' }],
        semaphors: [],
        version,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    return response;
  };

  const confirmOrDeleteCommit = async (
    requestType: string,
    objId: string | number,
    objType: string,
    version: number,
  ): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: requestType,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: objId,
        obj_type: objType,
        version,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj_id).toBe(objId);
    SchemaValidator.validate(confirmCommitSchema, response.body);
  };

  const showCommit = async (objId: string | number, objType: string, version: number): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: objId,
        obj_type: objType,
        version,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].list).not.toHaveLength(0);
  };

  const listConvAndVerify = async (
    convId: string | number,
    nodeId: string,
    expectedLogicType: string,
  ): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: convId,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].commits).toBeEmpty();
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === nodeId).logics[0].type).toBe(
      expectedLogicType,
    );
  };

  // Test data
  const apiCodeLogic = {
    type: NODE_LOGIC_TYPE.ApiCode,
    err_node_id: '',
    lang: 'js',
    src: 'var b = *********;data.a = b;',
  };
  const setParamLogic = {
    type: NODE_LOGIC_TYPE.SetParam,
    extra: { key: '{{object}}' },
    extra_type: { key: 'object' },
    err_node_id: '',
  };

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: `projectsn-${Date.now()}`,
        stages: ['dev', 'pre'],
        status: 'active',
      }),
    );
    expect(responseProject.status).toBe(RESP_STATUS.OK);
    expect(responseProject.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    newProject = responseProject.body.ops[0].obj_id;
    stageDev = responseProject.body.ops[0].stages[0];
    stagePre = responseProject.body.ops[0].stages[1];

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `Folder_${Date.now()}`,
        project_id: newProject,
        stage_id: stageDev,
      }),
    );
    expect(responseFolder.status).toBe(RESP_STATUS.OK);
    expect(responseFolder.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    folderInStage = responseFolder.body.ops[0].obj_id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: folderInStage,
        title: `Process_${Date.now()}`,
        project_id: newProject,
        stage_id: stageDev,
      }),
    );
    expect(responseConv.status).toBe(RESP_STATUS.OK);
    expect(responseConv.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    convInFolderOne = responseConv.body.ops[0].obj_id;

    const responseListProcOne = await requestListConv(api, convInFolderOne, company_id, newProject, stageDev);
    expect(responseListProcOne.status).toBe(RESP_STATUS.OK);
    nodeIdProcOne = (responseListProcOne.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    finNodeIdProcOne = (responseListProcOne.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseConv2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: folderInStage,
        title: `Process_${Date.now()}`,
        project_id: newProject,
        stage_id: stageDev,
      }),
    );
    expect(responseConv2.status).toBe(RESP_STATUS.OK);
    expect(responseConv2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    convInFolderTwo = responseConv2.body.ops[0].obj_id;

    const responseListProcTwo = await requestListConv(api, convInFolderTwo, company_id, newProject, stageDev);
    expect(responseListProcTwo.status).toBe(RESP_STATUS.OK);
    nodeIdProcTwo = (responseListProcTwo.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    finNodeIdProcTwo = (responseListProcTwo.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseConv3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: stageDev,
        title: `Process_${Date.now()}`,
        project_id: newProject,
        stage_id: stageDev,
      }),
    );
    expect(responseConv3.status).toBe(RESP_STATUS.OK);
    expect(responseConv3.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    convInStage = responseConv3.body.ops[0].obj_id;

    const responseListProcThree = await requestListConv(api, convInStage, company_id, newProject, stageDev);
    expect(responseListProcThree.status).toBe(RESP_STATUS.OK);
    nodeIdProcThree = (responseListProcThree.body.ops[0].list as Array<any>).find(item => item.title === 'process')
      .obj_id;
    finNodeIdProcThree = (responseListProcThree.body.ops[0].list as Array<any>).find(item => item.title === 'final')
      .obj_id;

    const responseConv4 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: stagePre,
        title: `Process_${Date.now()}`,
        project_id: newProject,
        stage_id: stagePre,
      }),
    );
    expect(responseConv4.status).toBe(RESP_STATUS.OK);
    expect(responseConv4.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    convInOtherStage = responseConv4.body.ops[0].obj_id;

    const responseListProcFour = await requestListConv(api, convInOtherStage, company_id, newProject, stagePre);
    expect(responseListProcFour.status).toBe(RESP_STATUS.OK);
    nodeIdProcFour = (responseListProcFour.body.ops[0].list as Array<any>).find(item => item.title === 'process')
      .obj_id;
    finNodeIdProcFour = (responseListProcFour.body.ops[0].list as Array<any>).find(item => item.title === 'final')
      .obj_id;
  });

  test('should confirm commit obj_type conv', async () => {
    await modifyNode(nodeIdProcOne, convInFolderOne, finNodeIdProcOne, apiCodeLogic, 22);

    await confirmOrDeleteCommit(REQUEST_TYPE.CONFIRM, convInFolderOne, OBJ_TYPE.CONV, 22);

    await listConvAndVerify(convInFolderOne, nodeIdProcOne, NODE_LOGIC_TYPE.ApiCode);

    await showCommit(convInFolderOne, OBJ_TYPE.CONV, 22);
  });

  test('should confirm commit obj_type folder', async () => {
    await modifyNode(nodeIdProcOne, convInFolderOne, finNodeIdProcOne, setParamLogic, 23);

    await modifyNode(nodeIdProcTwo, convInFolderTwo, finNodeIdProcTwo, setParamLogic, 23);

    await confirmOrDeleteCommit(REQUEST_TYPE.CONFIRM, folderInStage, OBJ_TYPE.FOLDER, 23);

    await listConvAndVerify(convInFolderOne, nodeIdProcOne, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInFolderTwo, nodeIdProcTwo, NODE_LOGIC_TYPE.SetParam);

    await showCommit(folderInStage, OBJ_TYPE.FOLDER, 23);
  });

  test('should confirm commit obj_type stage', async () => {
    await modifyNode(nodeIdProcOne, convInFolderOne, finNodeIdProcOne, apiCodeLogic, 24);

    await modifyNode(nodeIdProcTwo, convInFolderTwo, finNodeIdProcTwo, apiCodeLogic, 24);

    await modifyNode(nodeIdProcThree, convInStage, finNodeIdProcThree, apiCodeLogic, 24);

    await confirmOrDeleteCommit(REQUEST_TYPE.CONFIRM, stageDev, OBJ_TYPE.STAGE, 24);

    await listConvAndVerify(convInFolderOne, nodeIdProcOne, NODE_LOGIC_TYPE.ApiCode);
    await listConvAndVerify(convInFolderTwo, nodeIdProcTwo, NODE_LOGIC_TYPE.ApiCode);
    await listConvAndVerify(convInStage, nodeIdProcThree, NODE_LOGIC_TYPE.ApiCode);

    await showCommit(stageDev, OBJ_TYPE.STAGE, 24);
  });

  test('should confirm commit obj_type project', async () => {
    await modifyNode(nodeIdProcOne, convInFolderOne, finNodeIdProcOne, setParamLogic, 25);

    await modifyNode(nodeIdProcTwo, convInFolderTwo, finNodeIdProcTwo, setParamLogic, 25);

    await modifyNode(nodeIdProcThree, convInStage, finNodeIdProcThree, setParamLogic, 25);

    await modifyNode(nodeIdProcFour, convInOtherStage, finNodeIdProcFour, setParamLogic, 25);

    await confirmOrDeleteCommit(REQUEST_TYPE.CONFIRM, newProject, OBJ_TYPE.PROJECT, 25);

    await listConvAndVerify(convInFolderOne, nodeIdProcOne, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInFolderTwo, nodeIdProcTwo, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInStage, nodeIdProcThree, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInOtherStage, nodeIdProcFour, NODE_LOGIC_TYPE.SetParam);

    await showCommit(newProject, OBJ_TYPE.PROJECT, 25);
  });

  test('should delete commit obj_type conv', async () => {
    await modifyNode(nodeIdProcOne, convInFolderOne, finNodeIdProcOne, apiCodeLogic, 26);

    await confirmOrDeleteCommit(REQUEST_TYPE.DELETE, convInFolderOne, OBJ_TYPE.CONV, 26);

    await listConvAndVerify(convInFolderOne, nodeIdProcOne, NODE_LOGIC_TYPE.SetParam);
  });

  test('should delete commit obj_type folder', async () => {
    await modifyNode(nodeIdProcOne, convInFolderOne, finNodeIdProcOne, setParamLogic, 26);

    await modifyNode(nodeIdProcTwo, convInFolderTwo, finNodeIdProcTwo, setParamLogic, 26);

    await confirmOrDeleteCommit(REQUEST_TYPE.DELETE, folderInStage, OBJ_TYPE.FOLDER, 26);

    await listConvAndVerify(convInFolderOne, nodeIdProcOne, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInFolderTwo, nodeIdProcTwo, NODE_LOGIC_TYPE.SetParam);
  });

  test('should delete commit obj_type stage', async () => {
    await modifyNode(nodeIdProcOne, convInFolderOne, finNodeIdProcOne, apiCodeLogic, 27);

    await modifyNode(nodeIdProcTwo, convInFolderTwo, finNodeIdProcTwo, apiCodeLogic, 27);

    await modifyNode(nodeIdProcThree, convInStage, finNodeIdProcThree, apiCodeLogic, 27);

    await confirmOrDeleteCommit(REQUEST_TYPE.DELETE, stageDev, OBJ_TYPE.STAGE, 27);

    await listConvAndVerify(convInFolderOne, nodeIdProcOne, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInFolderTwo, nodeIdProcTwo, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInStage, nodeIdProcThree, NODE_LOGIC_TYPE.SetParam);
  });

  test('should delete commit obj_type project', async () => {
    await modifyNode(nodeIdProcOne, convInFolderOne, finNodeIdProcOne, setParamLogic, 28);

    await modifyNode(nodeIdProcTwo, convInFolderTwo, finNodeIdProcTwo, setParamLogic, 28);

    await modifyNode(nodeIdProcThree, convInStage, finNodeIdProcThree, setParamLogic, 28);

    await modifyNode(nodeIdProcFour, convInOtherStage, finNodeIdProcFour, setParamLogic, 28);

    await confirmOrDeleteCommit(REQUEST_TYPE.DELETE, newProject, OBJ_TYPE.PROJECT, 28);

    await listConvAndVerify(convInFolderOne, nodeIdProcOne, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInFolderTwo, nodeIdProcTwo, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInStage, nodeIdProcThree, NODE_LOGIC_TYPE.SetParam);
    await listConvAndVerify(convInOtherStage, nodeIdProcFour, NODE_LOGIC_TYPE.SetParam);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
