import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listConvSchema from '../../../../schemas/v2/actions-objects/listObjsConvs.schema.json';
import listApiSchema from '../../../../schemas/v2/actions-objects/listObjsApiKeys.schema.json';
import listUserSchema from '../../../../schemas/v2/actions-objects/listObjsUsers.schema.json';
import listCompanySchema from '../../../../schemas/v2/actions-objects/listObjsCompany.schema.json';
import {
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestConfirm,
} from '../../../../../../../application/api/ApiObj';

describe('Obj List (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newApiKey: string | number;
  let newConvProject1: string | number;
  let newConvProject2: string | number;
  let newConvProject3: string | number;
  let newConvCompany: string | number;
  let newConvMyC: string | number;
  let newApiKeyCompany: string | number;
  let newApiKeyMyCorezoid: string | number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let newApi: ApiKeyClient;
  let api_company_name: string;
  let api_mc_name: string;
  let process_node_ID1: string | number;
  let final_node_ID1: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    api_company_name = `keyCompany-${Date.now()}`;
    api_mc_name = `keyMC-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: api_company_name,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseKey.status).toBe(200);
    expect(responseKey.body.ops[0].obj).toBe('user');
    const user = responseKey.body;
    const newApiKeyObject = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKeyObject);
    newApiKey = +newApiKeyObject.id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_id: newProject,

        obj_to: 'user',
        obj_to_id: newApiKey,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
        ],
      }),
    );
    expect(responseLink.status).toBe(RESP_STATUS.OK);
    expect(responseLink.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseLink.body.ops[0].obj_to_id).toBe(newApiKey);

    const responseConvProject1 = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Conv_${Date.now()}`,
      newStage,
      'process',
      newProject,
      newStage,
    );
    newConvProject1 = responseConvProject1.body.ops[0].obj_id;
    const responseList = await requestListConv(api, newConvProject1, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    process_node_ID1 = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID1 = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateSetParam = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID1,
        conv_id: newConvProject1,
        title: 'set_param',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: { key: '{{object}}' },
            extra_type: { key: 'object' },
            err_node_id: '',
          },
          { to_node_id: final_node_ID1, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateSetParam.status).toBe(RESP_STATUS.OK);
    expect(responseCreateSetParam.body.ops[0].proc).toEqual('ok');

    const responseCommit = await requestConfirm(api, newConvProject1, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const responseConvProject2 = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Conv_${Date.now()}`,
      newStage,
      'process',
      newProject,
      newStage,
    );
    newConvProject2 = responseConvProject2.body.ops[0].obj_id;

    const responseConvProject3 = await requestCreateObj(
      newApi,
      OBJ_TYPE.CONV,
      company_id,
      `Conv_${Date.now()}`,
      newStage,
      'process',
      newProject,
      newStage,
    );
    newConvProject3 = responseConvProject3.body.ops[0].obj_id;

    const responseConvMyC = await requestCreateObj(api, OBJ_TYPE.CONV, null, `Conv_${Date.now()}`, 0);
    newConvMyC = responseConvMyC.body.ops[0].obj_id;

    const responseConvCompany = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, 0);
    newConvCompany = responseConvCompany.body.ops[0].obj_id;

    const responseApiKeyCompany = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: api_company_name,
        logins: [{ type: 'api' }],
      }),
    );
    newApiKeyCompany = responseApiKeyCompany.body.ops[0].users[0].obj_id;

    const responseApiKeyMyCorezoid = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: api_mc_name,
        logins: [{ type: 'api' }],
      }),
    );
    newApiKeyMyCorezoid = responseApiKeyMyCorezoid.body.ops[0].users[0].obj_id;

    await new Promise(r => setTimeout(r, 20000));
  });

  test(`should list Objs (conv) in project/stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,

        pattern: `${newConvProject1}`,
        filter: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list[0].obj_id).toBe(newConvProject1);
    SchemaValidator.validate(listConvSchema, response.body);
  });

  test(`should list Objs (conv) by node title in project/stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,

        pattern: `set_param`,
        filter: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list[0].obj_id).toBe(newConvProject1);
    SchemaValidator.validate(listConvSchema, response.body);
  });

  test(`should list Objs (conv) in project/stage with limit=1 by name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,

        pattern: 'Conv_',
        filter: 'conv',
        project_id: newProject,
        stage_id: newStage,
        limit: 1,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvProject1 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvProject2 })]),
    );
    SchemaValidator.validate(listConvSchema, response.body);
  });

  test(`should list Objs (conv) in project/stage with limit=1000 by name all`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,

        pattern: 'Conv',
        filter: 'conv',
        project_id: newProject,
        stage_id: newStage,
        limit: 1000,
        only_owner: false,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvProject1 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvProject2 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvProject3 })]),
    );
    SchemaValidator.validate(listConvSchema, response.body);
  });

  test(`should list objs (conv) in project/stage with limit=10 by name (only_owner)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,

        pattern: 'Conv',
        filter: 'conv',
        project_id: newProject,
        stage_id: newStage,
        limit: 10,
        only_owner: true,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvProject1 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvProject2 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvProject3 })]),
    );
    SchemaValidator.validate(listConvSchema, response.body);
  });

  test(`should list objs (conv) in My Corezoid`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,

        pattern: `${newConvMyC}`,
        filter: 'conv',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list[0].obj_id).toBe(newConvMyC);
    SchemaValidator.validate(listConvSchema, response.body);
  });

  test(`should list objs (conv) in Company (without filter)`, async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,

        pattern: `${newConvCompany}`,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list[0].obj_id).toBe(newConvCompany);
    SchemaValidator.validate(listConvSchema, response.body);
  });

  test(`should list objs (user) in Company`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,

        pattern: api_company_name,
        filter: 'user',
        user_type: 'api',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list[0].obj_type).toBe('user');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newApiKeyCompany })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newApiKey })]));
    SchemaValidator.validate(listApiSchema, response.body);
  });

  test(`should list objs (user) in My Corezoid`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,

        pattern: api_mc_name,
        filter: 'user',
        user_type: 'api',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list[0].obj_type).toBe('user');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newApiKeyMyCorezoid })]),
    );
    SchemaValidator.validate(listApiSchema, response.body);
  });

  test(`should list objs (company)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,

        pattern: 'test',
        filter: 'company',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list[0].obj_type).toBe('company');
    SchemaValidator.validate(listCompanySchema, response.body);
  });

  test(`should list objs (user) email`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,

        pattern: 'starttesting5',
        filter: 'user',
        user_type: 'user',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list[0].obj_type).toBe('user');
    SchemaValidator.validate(listUserSchema, response.body);
  });

  test(`should list objs (user) Name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,

        pattern: 'Auto',
        filter: 'user',
        user_type: 'user',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('objs');
    expect(response.body.ops[0].list[0].obj_type).toBe('user');
    SchemaValidator.validate(listUserSchema, response.body);
  });

  afterAll(async () => {
    const responseConvMyCorezoid = await requestDeleteObj(api, OBJ_TYPE.CONV, newConvMyC, null);
    expect(responseConvMyCorezoid.status).toBe(200);

    const responseConvCompany = await requestDeleteObj(api, OBJ_TYPE.CONV, newConvCompany, company_id);
    expect(responseConvCompany.status).toBe(200);

    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);

    const responseKeyMyCorezoid = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKeyMyCorezoid, null);
    expect(responseKeyMyCorezoid.status).toBe(200);

    const responseKeyCompany = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKeyCompany, company_id);
    expect(responseKeyCompany.status).toBe(200);

    const responseKeyCompanyNew = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKey, company_id);
    expect(responseKeyCompanyNew.status).toBe(200);
  });
});
