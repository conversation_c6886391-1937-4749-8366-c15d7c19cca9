# API v2 Pre-Production Tests

This directory contains Playwright TypeScript tests migrated from JMeter for API v2 pre-production testing.

## Task Parameter Migration (COR-12118)

Successfully migrated JMeter "Task Parameter" ThreadGroup tests from `API_v2_Amazon.jmx` to TypeScript/Playwright with exact operation sequences.

### Migration Overview

- **Source**: JMeter ThreadGroup "Task Parameter" from API_v2_Amazon.jmx (4.5MB file)
- **Target**: `task_parameter.test.ts` 
- **Test Count**: 8 Add_task test scenarios with specific operation sequences
- **Migration Status**: ✅ Complete

### Test Scenarios with Operation Sequences

The migration includes 8 comprehensive test scenarios, each following exact operation sequences from the JMeter ThreadGroup:

1. **№1 VIEW API 2 Add_task #1**: create task → list node → modify conv_params → list node → modify conv_params → list node
2. **№2 VIEW API 2 Add_task #2**: create task → list node → modify conv_params → list node → modify conv_params → list node  
3. **VIEW API 2 Add_task #3**: create task → list node → modify conv_params → list node → show task
4. **VIEW API 2 Add_task #4**: create task → list node → modify conv_params → list node
5. **VIEW API 2 Add_task #5**: create task → list node → modify conv_params → list node → modify conv_params → list node
6. **№7 VIEW API 2 Add_task #6**: create task → list node → modify conv_params → list node → modify conv_params
7. **VIEW API 2 Add_task #7 (n) wrong_validate_params**: create task → modify conv_params
8. **VIEW API 2 Add_task #8**: create task → list node → Reset_node → list node

### Data Structures Tested

- **Complex Nested Objects**: Tests with nested data structures and arrays (`s: {f: 567, r: {t: 9870}}`)
- **Array Structures**: Multi-dimensional arrays with nested test objects (`array: [{test: [{qw: 4567}]}]`)
- **Product Lists**: Boolean values, empty objects/arrays (`productList: ['567', '789', {l: 456}], bool: true`)
- **Multi-dimensional Arrays**: Complex nested array structures (`arrey2m: [{arey: [[{ar1: 123}, {ar1: 345}]]}]`)
- **Null Object Testing**: Handling null values in task data (`objnull: null`)
- **Simple Test Types**: Basic parameter configurations (`testtype: 123`)

### Operation Types Implemented

- **Create Task**: Task creation with various data structures
- **List Node**: Node listing operations for verification
- **Modify Conv_Params**: Parameter configuration with flags (input, auto-clear), types (string, number, object), and complex paths
- **Show Task**: Task retrieval by reference
- **Reset_node**: Node reset operations (unique to test 8)

### Key Features

- **Setup/Cleanup**: Proper conveyor lifecycle management with beforeAll/afterAll
- **API Integration**: Uses ApiKeyClient following established patterns
- **Data Structures**: Preserves complex nested objects, arrays, and special cases from JMeter BeanShell scripts
- **Operation Sequences**: Each test follows exact user-specified sequence from JMeter analysis
- **Parameter Configurations**: Comprehensive modify conv_params with nested paths, arrays, and object types
- **Assertions**: Comprehensive response validation and error handling
- **Timing**: Maintains 3-second pauses between tests as per original JMeter configuration

### Usage

```bash
# Run the task parameter tests
npm test tests/api2pre/task_parameter.test.ts

# Run all API v2 pre-production tests
npm test tests/api2pre/
```

### Implementation Details

- **Framework**: Jest with Playwright patterns
- **Authentication**: API key-based authentication via ApiKeyClient
- **Utilities**: Leverages existing `createRequestWithOps`, `OBJ_TYPE`, `REQUEST_TYPE` constants
- **Error Handling**: Proper status code and process status validation
- **Code Quality**: Follows established codebase conventions and patterns
- **Operation Mapping**: Direct 1:1 mapping from JMeter BeanShell scripts to TypeScript

### Migration Quality

- ✅ High fidelity migration preserving exact data structures from JMeter BeanShell scripts
- ✅ Exact operation sequences as specified by user requirements
- ✅ Follows established code patterns from existing TaskParameter.test.ts
- ✅ Comprehensive test coverage for all 8 Add_task scenarios with their complete operation flows
- ✅ All operation types implemented: create task, list node, modify conv_params, show task, Reset_node
- ✅ Complex parameter configurations for modify conv_params operations
- ✅ Proper TypeScript typing and error handling
- ✅ Clean, readable code following style guidelines

---

For detailed migration analysis and technical implementation notes, see `task_parameter_migration_audit.md` in the project root.
