import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj, requestListConv, requestList } from '../../../../application/api/ApiObj';

describe('Task Parameter Migration from JMeter - Add_task Tests', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let final_node_ID: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `TaskParameterTest`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });

  test('№1 VIEW API 2 Add_task #1: create task, list node, modify conv_params, list node, modify conv_params, list node', async () => {
    const addTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        conv_id: conv_id,
        obj: OBJ_TYPE.TASK,
        data: {
          a: '123',
          b: 456,
          c: '5168755504343308',
          'r.d': '897',
          s: { f: 567, r: { t: 9870 } },
          arr: [{ v: 567 }, { w: 5676 }, 678, { q: [{ e: 'e' }] }],
          g: '2345',
        },
        ref: `ref_${Date.now()}`,
      }),
    );

    expect(addTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(addTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.ops[0].obj).toBe('task');
    expect(addTaskResponse.body.ops[0].obj_id).toBeDefined();

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse1 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse1.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse1.body.ops[0].count).toBe(1);
    expect(listNodeResponse1.body.ops[0].list[0].data.a).toBe('123');
    expect(listNodeResponse1.body.ops[0].list[0].data.b).toBe(456);
    expect(listNodeResponse1.body.ops[0].list[0].data.c).toBe('5168755504343308');
    expect(listNodeResponse1.body.ops[0].list[0].data['r.d']).toBe('897');
    expect(listNodeResponse1.body.ops[0].list[0].data.s).toEqual({ f: 567, r: { t: 9870 } });
    expect(listNodeResponse1.body.ops[0].list[0].data.arr).toEqual([{ v: 567 }, { w: 5676 }, 678, { q: [{ e: 'e' }] }]);
    expect(listNodeResponse1.body.ops[0].list[0].data.g).toBe('2345');

    const modifyConvParamsResponse1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [
          { name: 'a', descr: 'aa', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'b', descr: 'bb', type: 'number', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'c', descr: 'cc', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'r.d', descr: 'rr', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 's.f', descr: 'f', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 's.r.t', descr: 'f', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'g', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'arr[0].v', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'arr[2]', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
        ],
      }),
    );

    expect(modifyConvParamsResponse1.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse1.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse2 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse2.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse2.body.ops[0].count).toBe(1);
    expect(listNodeResponse2.body.ops[0].list[0].data.a).toBe('123');
    expect(listNodeResponse2.body.ops[0].list[0].data.b).toBe(456);
    expect(listNodeResponse2.body.ops[0].list[0].data.c).toBe('5168755504343308');
    expect(listNodeResponse2.body.ops[0].list[0].data['r.d']).toBe('897');
    expect(listNodeResponse2.body.ops[0].list[0].data.s).toEqual({ f: 567, r: { t: 9870 } });
    expect(listNodeResponse2.body.ops[0].list[0].data.arr).toEqual([{ v: 567 }, { w: 5676 }, 678, { q: [{ e: 'e' }] }]);
    expect(listNodeResponse2.body.ops[0].list[0].data.g).toBe('2345');

    const modifyConvParamsResponse2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [
          { name: 'a', descr: 'aa', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'c', descr: 'cc', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 's.r.t', descr: 'f', type: 'string', flags: ['auto-clear'], regex: '', regex_error_text: '' },
          { name: 'g', descr: 'g', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
        ],
      }),
    );

    expect(modifyConvParamsResponse2.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse2.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse3 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse3.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse3.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse3.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse3.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse3.body.ops[0].count).toBe(1);
    expect(listNodeResponse3.body.ops[0].list[0].data.a).toBe('***');
    expect(listNodeResponse3.body.ops[0].list[0].data.b).toBe(456);
    expect(listNodeResponse3.body.ops[0].list[0].data.c).toBe('***');
    expect(listNodeResponse3.body.ops[0].list[0].data['r.d']).toBe('897');
    expect(listNodeResponse3.body.ops[0].list[0].data.s).toEqual({ f: 567, r: { t: '***' } });
    expect(listNodeResponse3.body.ops[0].list[0].data.arr).toEqual([{ v: 567 }, { w: 5676 }, 678, { q: [{ e: 'e' }] }]);
    expect(listNodeResponse3.body.ops[0].list[0].data.g).toBe('***');

    await new Promise(resolve => setTimeout(resolve, 3000));
  });

  test('№2 VIEW API 2 Add_task #2: create task, list node, modify conv_params, list node, modify conv_params, list node', async () => {
    const addTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        conv_id: conv_id,
        obj: OBJ_TYPE.TASK,
        data: {
          a: '123',
          b: 456,
          c: '5168755504343308',
          'r.d': '897',
          s: { f: 567, r: { t: 9870 } },
          arr: [{ v: 567 }, { w: 5676 }, 678, { q: [{ e: 'e' }] }],
          g: '2345',
        },
        ref: '123',
      }),
    );

    expect(addTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(addTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.ops[0].obj).toBe('task');
    expect(addTaskResponse.body.ops[0].obj_id).toBeDefined();

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse1 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse1.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse1.body.ops[0].count).toBe(2);
    expect(listNodeResponse1.body.ops[0].list[0].data.a).toBe('***');
    expect(listNodeResponse1.body.ops[0].list[0].data.b).toBe(456);
    expect(listNodeResponse1.body.ops[0].list[0].data.c).toBe('***');
    expect(listNodeResponse1.body.ops[0].list[0].data['r.d']).toBe('897');
    expect(listNodeResponse1.body.ops[0].list[0].data.s).toEqual({ f: 567, r: { t: '***' } });
    expect(listNodeResponse1.body.ops[0].list[0].data.arr).toEqual([{ v: 567 }, { w: 5676 }, 678, { q: [{ e: 'e' }] }]);
    expect(listNodeResponse1.body.ops[0].list[0].data.g).toBe('***');

    const modifyConvParamsResponse1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [
          { name: 'a', descr: 'aa', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'b', descr: 'bb', type: 'number', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'c', descr: 'cc', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'r.d', descr: 'rr', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 's.f', descr: 'f', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 's.r.t', descr: 'f', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'g', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'arr[0].v', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'arr[2]', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
        ],
      }),
    );

    expect(modifyConvParamsResponse1.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse1.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse2 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse2.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse2.body.ops[0].count).toBe(2);
    expect(listNodeResponse2.body.ops[0].list[0].data.a).toBe('123');
    expect(listNodeResponse2.body.ops[0].list[0].data.b).toBe(456);
    expect(listNodeResponse2.body.ops[0].list[0].data.c).toBe('5168755504343308');
    expect(listNodeResponse2.body.ops[0].list[0].data['r.d']).toBe('897');
    expect(listNodeResponse2.body.ops[0].list[0].data.s).toEqual({ f: 567, r: { t: 9870 } });
    expect(listNodeResponse2.body.ops[0].list[0].data.arr).toEqual([{ v: 567 }, { w: 5676 }, 678, { q: [{ e: 'e' }] }]);
    expect(listNodeResponse2.body.ops[0].list[0].data.g).toBe('2345');

    const modifyConvParamsResponse2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [
          { name: 'a', descr: 'aa', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'c', descr: 'cc', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 's.r.t', descr: 'f', type: 'string', flags: ['auto-clear'], regex: '', regex_error_text: '' },
          { name: 'g', descr: 'g', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          {
            name: 'arr[3].q[0].e',
            descr: 'g',
            type: 'string',
            flags: ['input', 'auto-clear'],
            regex: '',
            regex_error_text: '',
          },
        ],
      }),
    );

    expect(modifyConvParamsResponse2.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse2.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse3 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse3.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse3.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse3.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse3.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse3.body.ops[0].count).toBe(2);
    expect(listNodeResponse3.body.ops[0].list[0].data.a).toBe('***');
    expect(listNodeResponse3.body.ops[0].list[0].data.b).toBe(456);
    expect(listNodeResponse3.body.ops[0].list[0].data.c).toBe('***');
    expect(listNodeResponse3.body.ops[0].list[0].data['r.d']).toBe('897');
    expect(listNodeResponse3.body.ops[0].list[0].data.s).toEqual({ f: 567, r: { t: '***' } });
    expect(listNodeResponse3.body.ops[0].list[0].data.arr).toEqual([
      { v: 567 },
      { w: 5676 },
      678,
      { q: [{ e: '***' }] },
    ]);
    expect(listNodeResponse3.body.ops[0].list[0].data.g).toBe('***');

    await new Promise(resolve => setTimeout(resolve, 3000));
  });

  test('VIEW API 2 Add_task #3: create task, list node, modify conv_params, list node, show task', async () => {
    const addTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        conv_id: conv_id,
        obj: OBJ_TYPE.TASK,
        data: {
          array: [{ test: [{ qw: 4567 }] }, { test: [{ qw: '987' }] }, '6781', { test: [{ qw: 123 }] }],
          array1: [{ test: 4567 }, { test: 987 }, { test: 123 }],
        },
        ref: '123-3',
      }),
    );

    expect(addTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(addTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.ops[0].obj).toBe('task');
    expect(addTaskResponse.body.ops[0].obj_id).toBeDefined();

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse1 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse1.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse1.body.ops[0].count).toBe(3);
    expect(listNodeResponse1.body.ops[0].list[0].data.array).toEqual([
      { test: [{ qw: 4567 }] },
      { test: [{ qw: '987' }] },
      '6781',
      { test: [{ qw: 123 }] },
    ]);
    expect(listNodeResponse1.body.ops[0].list[0].data.array1).toEqual([{ test: 4567 }, { test: 987 }, { test: 123 }]);

    const modifyConvParamsResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [
          { name: 'array[].test[].qw', descr: 'g', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'array1[].test', descr: 'g', type: 'string', flags: [], regex: '', regex_error_text: '' },
        ],
      }),
    );

    expect(modifyConvParamsResponse.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse2 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse2.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse2.body.ops[0].count).toBe(3);
    expect(listNodeResponse2.body.ops[0].list[0].data.array).toEqual([
      { test: [{ qw: 4567 }] },
      { test: [{ qw: '987' }] },
      '6781',
      { test: [{ qw: 123 }] },
    ]);
    expect(listNodeResponse2.body.ops[0].list[0].data.array1).toEqual([{ test: 4567 }, { test: 987 }, { test: 123 }]);

    const showTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        conv_id: conv_id,
        obj: OBJ_TYPE.TASK,
        ref: '123-3',
      }),
    );

    expect(showTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(showTaskResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(showTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(showTaskResponse.body.ops[0].obj_id).toBeDefined();
    expect(showTaskResponse.body.ops[0].data.array).toEqual([
      { test: [{ qw: 4567 }] },
      { test: [{ qw: '987' }] },
      '6781',
      { test: [{ qw: 123 }] },
    ]);
    expect(showTaskResponse.body.ops[0].data.array1).toEqual([{ test: 4567 }, { test: 987 }, { test: 123 }]);

    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  test('VIEW API 2 Add_task #4: create task, list node, modify conv_params, list node', async () => {
    const addTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        conv_id: conv_id,
        obj: OBJ_TYPE.TASK,
        data: {
          productList: ['567', '789', { l: 456 }],
          b: [{ key1: 123 }, { key1: 345 }],
          e: {},
          f: [],
          bool: true,
        },
        ref: '1234',
      }),
    );

    expect(addTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(addTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.ops[0].obj).toBe('task');
    expect(addTaskResponse.body.ops[0].obj_id).toBeDefined();

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse1 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse1.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse1.body.ops[0].count).toBe(4);
    expect(listNodeResponse1.body.ops[0].list[0].data.productList).toEqual(['567', '789', { l: 456 }]);
    expect(listNodeResponse1.body.ops[0].list[0].data.b).toEqual([{ key1: 123 }, { key1: 345 }]);
    expect(listNodeResponse1.body.ops[0].list[0].data.e).toEqual({});
    expect(listNodeResponse1.body.ops[0].list[0].data.f).toEqual([]);
    expect(listNodeResponse1.body.ops[0].list[0].data.bool).toBe(true);

    const modifyConvParamsResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [
          { name: 'a', descr: 'aa', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'b', descr: 'bb', type: 'number', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'c', descr: 'cc', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'r.d', descr: 'rr', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 's.f', descr: 'f', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 's.r.t', descr: 'f', type: 'string', flags: ['auto-clear'], regex: '', regex_error_text: '' },
          { name: 'g', descr: 'g', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'arr[0].v', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'arr[2]', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'array[].test[].qw', descr: 'g', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'array1[].test', descr: 'g', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'productList[]', descr: 'productList[]', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'b[].key1', descr: 'b[].key1', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'e', descr: 'e', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'f', descr: 'f', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'bool', descr: 'bool', type: 'string', flags: [], regex: '', regex_error_text: '' },
        ],
      }),
    );

    expect(modifyConvParamsResponse.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse2 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse2.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse2.body.ops[0].count).toBe(4);
    expect(listNodeResponse2.body.ops[0].list[0].data.productList).toEqual(['567', '789', { l: 456 }]);
    expect(listNodeResponse2.body.ops[0].list[0].data.b).toEqual([{ key1: 123 }, { key1: 345 }]);
    expect(listNodeResponse2.body.ops[0].list[0].data.e).toEqual({});
    expect(listNodeResponse2.body.ops[0].list[0].data.f).toEqual([]);
    expect(listNodeResponse2.body.ops[0].list[0].data.bool).toBe(true);

    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  test('VIEW API 2 Add_task #5: create task, list node, modify conv_params, list node, modify conv_params, list node', async () => {
    const addTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        conv_id: conv_id,
        obj: OBJ_TYPE.TASK,
        data: {
          arrey2m: [{ arey: [[{ ar1: 123 }, { ar1: 345 }]] }],
        },
        ref: `ref_${Date.now()}`,
      }),
    );

    expect(addTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(addTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.ops[0].obj).toBe('task');
    expect(addTaskResponse.body.ops[0].obj_id).toBeDefined();

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse1 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse1.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse1.body.ops[0].count).toBe(5);
    expect(listNodeResponse1.body.ops[0].list[0].data.arrey2m).toEqual([{ arey: [[{ ar1: 123 }, { ar1: 345 }]] }]);

    const modifyConvParamsResponse1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [
          {
            name: 'arrey2m[0].arey[0][0].ar1',
            descr: 'arrey2m[0].arey[0][0].ar1',
            type: 'string',
            flags: ['auto-clear', 'input'],
            regex: '',
            regex_error_text: '',
          },
        ],
      }),
    );

    expect(modifyConvParamsResponse1.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse1.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse2 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse2.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse2.body.ops[0].count).toBe(5);
    expect(listNodeResponse2.body.ops[0].list[0].data.arrey2m).toEqual([{ arey: [[{ ar1: '***' }, { ar1: 345 }]] }]);

    const modifyConvParamsResponse2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [
          {
            name: 'arrey2m[0].arey[0][0].ar1',
            descr: 'arrey2m[0].arey[0][0].ar1',
            type: 'string',
            flags: [],
            regex: '',
            regex_error_text: '',
          },
        ],
      }),
    );

    expect(modifyConvParamsResponse2.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse2.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse3 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse3.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse3.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse3.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse3.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse3.body.ops[0].count).toBe(5);
    expect(listNodeResponse3.body.ops[0].list[0].data.arrey2m).toEqual([{ arey: [[{ ar1: 123 }, { ar1: 345 }]] }]);

    await new Promise(resolve => setTimeout(resolve, 3000));
  });

  test('№7 VIEW API 2 Add_task #6: create task, list node, modify conv_params, list node, modify conv_params', async () => {
    const addTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        conv_id: conv_id,
        obj: OBJ_TYPE.TASK,
        data: {
          objnull: null,
        },
        ref: `ref_${Date.now()}`,
      }),
    );

    expect(addTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(addTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.ops[0].obj).toBe('task');
    expect(addTaskResponse.body.ops[0].obj_id).toBeDefined();

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse1 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse1.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse1.body.ops[0].count).toBe(6);
    expect(listNodeResponse1.body.ops[0].list[0].data.objnull).toBe(null);

    const modifyConvParamsResponse1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [{ name: 'objnull', descr: '', type: 'object', flags: ['input'], regex: '', regex_error_text: '' }],
      }),
    );

    expect(modifyConvParamsResponse1.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse1.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse2 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse2.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse2.body.ops[0].count).toBe(6);
    expect(listNodeResponse2.body.ops[0].list[0].data.objnull).toBe(null);

    const modifyConvParamsResponse2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [{ name: 'objnull', descr: '', type: 'object', flags: ['input'], regex: '', regex_error_text: '' }],
      }),
    );

    expect(modifyConvParamsResponse2.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse2.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 3000));
  });

  test('VIEW API 2 Add_task #7 (n) wrong_validate_params: create task, modify conv_params', async () => {
    const addTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        conv_id: conv_id,
        obj: OBJ_TYPE.TASK,
        data: {
          testtype: 123,
        },
        ref: `ref_${Date.now()}`,
      }),
    );

    expect(addTaskResponse.status).toBe(RESP_STATUS.OK);

    const modifyConvParamsResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj_id: conv_id,
        obj: 'conv_params' as any,
        ref_mask: false,
        params: [
          {
            name: 'arrey2m[0].arey[0][0].ar1',
            descr: 'arrey2m[0].arey[0][0].ar1',
            type: 'string',
            flags: [],
            regex: '',
            regex_error_text: '',
          },
          { name: 'objnull', descr: '', type: 'object', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'testtype', descr: '', type: 'number', flags: ['input'], regex: '', regex_error_text: '' },
        ],
      }),
    );

    expect(modifyConvParamsResponse.status).toBe(RESP_STATUS.OK);
    expect(modifyConvParamsResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(modifyConvParamsResponse.body.ops[0].obj).toBe('conv_params');

    await new Promise(resolve => setTimeout(resolve, 3000));
  });

  test('VIEW API 2 Add_task #8: create task, list node, Reset_node, list node', async () => {
    const addTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        conv_id: conv_id,
        obj: OBJ_TYPE.TASK,
        data: {
          testtype: 123,
        },
        ref: `ref_${Date.now()}`,
      }),
    );

    expect(addTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(addTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(addTaskResponse.body.ops[0].obj).toBe('task');
    expect(addTaskResponse.body.ops[0].obj_id).toBeDefined();

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse1 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse1.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse1.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse1.body.ops[0].count).toBe(8);
    expect(listNodeResponse1.body.ops[0].list[0].data.testtype).toBe(123);

    const resetNodeResponse = await api.request(
      createRequestWithOps({
        type: 'reset' as any,
        conv_id: conv_id,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id: company_id,
      }),
    );

    expect(resetNodeResponse.status).toBe(RESP_STATUS.OK);
    expect(resetNodeResponse.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(resetNodeResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(resetNodeResponse.body.ops[0].obj).toBe('node');
    expect(resetNodeResponse.body.ops[0].obj_id).toBeDefined();

    await new Promise(resolve => setTimeout(resolve, 2000));
    const listNodeResponse2 = await requestList(api, final_node_ID as number, conv_id, company_id);
    expect(listNodeResponse2.status).toBe(RESP_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.request_proc).toEqual(PROC_STATUS.OK);
    expect(listNodeResponse2.body.ops[0].obj_id).toBeDefined();
    expect(listNodeResponse2.body.ops[0].count).toBe(0);
  });
});
