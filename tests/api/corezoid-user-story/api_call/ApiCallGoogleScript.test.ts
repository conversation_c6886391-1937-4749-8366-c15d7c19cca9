import { requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import {
  createRequestWithOps,
  REQUEST_TYPE,
  OBJ_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';

describe('API Call Google Script Tests', (): void => {
  let testSetup: ApiCallTestSetup;
  let task_id: string | number;

  beforeAll(
    async (): Promise<void> => {
      testSetup = await setupApiCallTest('ApiCall_GoogleScript_Tests');
    },
  );

  const googleScriptTestCases = [
    {
      method: 'GET',
      endpoint: 'GET',
      requestFormat: 'Default',
      url:
        'https://script.google.com/macros/s/AKfycbzgOM9s91NE27HqQQ8NuBX0cOMdixOmwi7MsHnrK-vgr4BlnvPydNf6L68V8bsXRotW0w/exec',
      description: 'Google Script GET endpoint',
    },
    {
      method: 'POST',
      endpoint: 'POST',
      requestFormat: 'Default',
      url:
        'https://script.google.com/macros/s/AKfycbwrX8LP7NBBpCBhbwCEtvBiT_4r4xc4BD1FE9ORESoiDpDtyZvB1Ols29Q6NIRWwOG2rg/exec',
      description: 'Google Script POST endpoint',
    },
  ];

  describe.each(googleScriptTestCases)(
    '$description - Method: $method, Format: $requestFormat',
    ({ method, requestFormat, url }): void => {
      test(`should correctly handle ${method} request to Google Script`, async (): Promise<void> => {
        const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

        const apiLogic: any = {
          type: 'api',
          format: '',
          method,
          url,
          extra: {},
          extra_type: {},
          max_threads: 5,
          err_node_id: '',
          extra_headers: { 'Content-Type': 'application/json' },
          send_sys: false,
          cert_pem: '',
          content_type: 'application/json',
        };

        const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

        const responseCreateNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID,
            conv_id,
            title: `GoogleScript_${method}_${requestFormat}`,
            description: `Test ${method} method to Google Script`,
            obj_type: 0,
            logics,
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseCreateNode.status).toBe(RESP_STATUS.OK);

        const responseCommit = await requestConfirm(api, conv_id, company_id);
        expect(responseCommit.status).toBe(RESP_STATUS.OK);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id,
            data: {
              test_data: `Test Google Script ${method}`,
              timestamp: Date.now(),
              source: 'corezoid_test',
            },
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(RESP_STATUS.OK);
        expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
        expect(responseTask.body.ops[0].obj).toEqual('task');
        task_id = responseTask.body.ops[0].obj_id;

        await new Promise(r => setTimeout(r, 4000));

        const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
        expect(responseShowTask.status).toBe(RESP_STATUS.OK);

        const taskData = responseShowTask.body.ops[0].data;
        expect(taskData.data).toBe(method);
        expect(taskData.test_data).toBe(`Test Google Script ${method}`);
        expect(taskData.source).toBe('corezoid_test');
      });
    },
  );

  afterAll(
    async (): Promise<void> => {
      await cleanupApiCallTest(testSetup);
    },
  );
});
