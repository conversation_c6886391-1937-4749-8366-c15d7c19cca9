import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { RESP_STATUS, PROC_STATUS } from '../../../../utils/corezoidRequest';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { requestList } from '../../../../application/api/ApiObj';
import { axiosInstance } from '../../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';

import {
  createCallbackConveyor,
  configureCallbackNode,
  commitConveyor,
  createTaskInConveyor,
  showTask,
  cleanupConveyor,
  CallbackConveyorSetup,
  parseNvpResponse,
  modifyConveyorStatus,
} from './helpers/callbackTestHelpers';

describe('Callback + Callback Mandrill', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;
  let company_id: any;
  let setup: CallbackConveyorSetup;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);

    setup = await createCallbackConveyor(api, company_id, 'CallbackMandrillConv');

    await configureCallbackNode(setup, {
      type: 'api_callback',
      obj_id_path: 'task_id',
    });

    await commitConveyor(setup);
  });

  test('should create task via direct callback URL json', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 1000));
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${setup.process_node_id}/${
      setup.hash
    }/${task_id}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545 },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 1000));

    const responseShow = await requestList(api, setup.final_node_id, setup.conv_id, company_id, 1);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].list[0].data.new).toBe(4545);
    expect(responseShow.body.ops[0].list[0].data.test_data).toBe('test_callback');
  });

  test('should create task via direct callback URL nvp', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 1000));
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/nvp/callback/${setup.process_node_id}/${
      setup.hash
    }/${task_id}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545 },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data).toContain('request_proc=ok');

    await new Promise(resolve => setTimeout(resolve, 1000));

    const responseShow = await requestList(api, setup.final_node_id, setup.conv_id, company_id, 1);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const jsonKey = '{"new":4545}';
    expect(responseShow.body.ops[0].list[0].data[jsonKey]).toBe(true);
    expect(responseShow.body.ops[0].list[0].data.test_data).toBe('test_callback');
  });

  test.each([
    { apiVersion: 'api/1', description: 'API v1' },
    { apiVersion: 'api/2', description: 'API v2' },
  ])('should modify task via direct callback URL without task_id - $description', async ({ apiVersion }) => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 1000));
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}${apiVersion}/json/callback/${
      setup.process_node_id
    }/${setup.hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545, task_id: `${task_id}` },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 1000));

    const responseShow = await requestList(api, setup.final_node_id, setup.conv_id, company_id, 1);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].list[0].data.new).toBe(4545);
    expect(responseShow.body.ops[0].list[0].data.test_data).toBe('test_callback');
  });

  test('should modify task via Mandrill plugin URL', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 1000));

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${
      setup.process_node_id
    }/${setup.hash}`;
    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      data: { task_id: `${task_id}`, c: 'mandrill_test' },
    });

    expect(mandrillResponse.status).toBe(RESP_STATUS.OK);
    expect(mandrillResponse.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(mandrillResponse.data.ops.obj).toEqual('task');

    await new Promise(r => setTimeout(r, 1000));

    const taskResponse = await showTask(setup, task_id);
    expect(taskResponse.status).toBe(RESP_STATUS.OK);
    expect(taskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const taskData = taskResponse.body.ops[0];
    expect(taskData.data.c).toBe('mandrill_test');
    expect(taskData.data.test_data).toBe('test_callback');
    expect(taskData.data.task_id).toBe(`${task_id}`);
  });

  test('should modify task via Mandrill events format', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 1000));

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${
      setup.process_node_id
    }/${setup.hash}`;
    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: `mandrill_events=[{ "task_id": "${task_id}", "c": "mandrill_events_test" }]`,
    });

    expect(mandrillResponse.status).toBe(RESP_STATUS.OK);
    expect(mandrillResponse.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(mandrillResponse.data.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 1000));

    const taskResponse = await showTask(setup, task_id);
    expect(taskResponse.status).toBe(RESP_STATUS.OK);
    expect(taskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const taskData = taskResponse.body.ops[0];
    expect(taskData.data.c).toBe('mandrill_events_test');
    expect(taskData.data.test_data).toBe('test_callback');
    expect(taskData.data.task_id).toBe(`${task_id}`);
  });

  test('should modify task via Mandrill events format with create task by public url', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${setup.conv_id}/${
      setup.hash
    }`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545 },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');

    await new Promise(r => setTimeout(r, 1000));

    const responseShow = await requestList(api, setup.process_node_id, setup.conv_id, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    const task_id = responseShow.body.ops[0].list[0].obj_id;

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${
      setup.process_node_id
    }/${setup.hash}`;
    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: `mandrill_events=[{ "task_id": "${task_id}", "c": "mandrill_events_test" }]`,
    });

    expect(mandrillResponse.status).toBe(RESP_STATUS.OK);
    expect(mandrillResponse.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(mandrillResponse.data.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 1000));

    const taskResponse = await showTask(setup, task_id);
    expect(taskResponse.status).toBe(RESP_STATUS.OK);
    expect(taskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const taskData = taskResponse.body.ops[0];
    expect(taskData.data.c).toBe('mandrill_events_test');
    expect(taskData.data.new).toBe(4545);
    expect(taskData.data.task_id).toBe(`${task_id}`);
  });

  test('should modify task', async () => {
    const { ref } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 1000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        conv_id: setup.conv_id,
        ref,
        data: { new: 4545 },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');

    await new Promise(r => setTimeout(r, 1000));

    const responseShow = await requestList(api, setup.final_node_id, setup.conv_id, company_id, 1);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].list[0].data.new).toBe(4545);
    expect(responseShow.body.ops[0].list[0].data.test_data).toBe('test_callback');
  });

  test('should modify task', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 1000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        conv_id: setup.conv_id,
        obj_id: task_id,
        node_id: setup.process_node_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');

    await new Promise(r => setTimeout(r, 1000));

    const taskResponse = await showTask(setup, task_id);
    expect(taskResponse.body.ops[0].proc).toEqual(PROC_STATUS.ERROR);

    const taskData = taskResponse.body.ops[0];
    expect(taskData.description).toBe('task not found');
  });

  test.each([
    { status: 'blocked', description: 'paused status' },
    { status: 'paused', description: 'blocked status' },
  ])(`shouldn't callback after modify status conv to $description`, async ({ status }) => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 1000));

    const response = await modifyConveyorStatus(api_s, setup.conv_id, status);
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');

    await new Promise(r => setTimeout(r, 1000));

    const urijson = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${
      setup.process_node_id
    }/${setup.hash}/${task_id}`;
    const responseTask = await axiosInstance({
      method: 'POST',
      url: urijson,
      data: { new: 4545 },
    });
    expect(responseTask.data.ops[0].proc).toEqual('**********************');

    const urinvp = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/nvp/callback/${setup.process_node_id}/${
      setup.hash
    }/${task_id}`;
    const responseTask1 = await axiosInstance({
      method: 'POST',
      url: urinvp,
      data: { new: 4545 },
    });
    const parsed = parseNvpResponse(responseTask1.data);
    expect(parsed.ops[0].proc).toBe('**********************');

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${
      setup.process_node_id
    }/${setup.hash}`;
    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      data: { task_id: `${task_id}`, c: 'mandrill_test' },
    });
    if (status === 'blocked') {
      expect(mandrillResponse.data).toEqual('Conveyor is blocked');
    } else {
      expect(mandrillResponse.data.ops.proc).toEqual('**********************');
    }

    const mandrilEventsUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${
      setup.process_node_id
    }/${setup.hash}`;
    const mandrillEVResponse = await axiosInstance({
      method: 'POST',
      url: mandrilEventsUri,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: `mandrill_events=[{ "task_id": "${task_id}", "c": "mandrill_events_test" }]`,
    });
    if (status === 'blocked') {
      expect(mandrillEVResponse.data).toEqual('Conveyor is blocked');
    } else {
      expect(mandrillEVResponse.data.ops[0].proc).toEqual('**********************');
    }

    const uriapi1 = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${
      setup.process_node_id
    }/${setup.hash}`;
    const responseapi1 = await axiosInstance({
      method: 'POST',
      url: uriapi1,
      data: { new: 4545, task_id: `${task_id}` },
    });
    if (status === 'blocked') {
      expect(responseapi1.data.description).toEqual('Conveyor is blocked');
    } else {
      expect(responseapi1.data.ops[0].proc).toEqual('**********************');
    }

    const uriapi2 = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/callback/${
      setup.process_node_id
    }/${setup.hash}`;
    const responseapi2 = await axiosInstance({
      method: 'POST',
      url: uriapi2,
      data: { new: 4545, task_id: `${task_id}` },
    });
    if (status === 'blocked') {
      expect(responseapi2.data.ops[0].description).toEqual('Conveyor is blocked');
    } else {
      expect(responseapi2.data.ops[0].proc).toEqual('**********************');
    }

    const responseActive = await modifyConveyorStatus(api_s, setup.conv_id, 'active');
    expect(responseActive.status).toBe(200);
  });

  test(`shouldn't callback after delete conv`, async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });

    await cleanupConveyor(setup);

    await new Promise(r => setTimeout(r, 1000));

    const urijson = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${
      setup.process_node_id
    }/${setup.hash}/${task_id}`;
    const responseTask = await axiosInstance({
      method: 'POST',
      url: urijson,
      data: { new: 4545 },
    });
    expect(responseTask.data.ops[0].proc).toEqual('conveyor_not_found');

    const urinvp = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/nvp/callback/${setup.process_node_id}/${
      setup.hash
    }/${task_id}`;
    const responseTask1 = await axiosInstance({
      method: 'POST',
      url: urinvp,
      data: { new: 4545 },
    });
    const parsed = parseNvpResponse(responseTask1.data);
    expect(parsed.ops[0].proc).toBe('conveyor_not_found');

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${
      setup.process_node_id
    }/${setup.hash}`;
    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      data: { task_id: `${task_id}`, c: 'mandrill_test' },
    });
    expect(mandrillResponse.data).toEqual('Conveyor is not found or deleted');

    const mandrilEventsUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${
      setup.process_node_id
    }/${setup.hash}`;
    const mandrillEVResponse = await axiosInstance({
      method: 'POST',
      url: mandrilEventsUri,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: `mandrill_events=[{ "task_id": "${task_id}", "c": "mandrill_events_test" }]`,
    });
    expect(mandrillEVResponse.data).toEqual('Conveyor is not found or deleted');

    const uriapi1 = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${
      setup.process_node_id
    }/${setup.hash}`;
    const responseapi1 = await axiosInstance({
      method: 'POST',
      url: uriapi1,
      data: { new: 4545, task_id: `${task_id}` },
    });
    expect(responseapi1.data.description).toEqual('Conveyor is not found or deleted');

    const uriapi2 = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/callback/${
      setup.process_node_id
    }/${setup.hash}`;
    const responseapi2 = await axiosInstance({
      method: 'POST',
      url: uriapi2,
      data: { new: 4545, task_id: `${task_id}` },
    });
    expect(responseapi2.data.ops[0].description).toEqual('Conveyor is not found or deleted');
  });

  afterAll(async () => {
    await cleanupConveyor(setup);
  });
});
