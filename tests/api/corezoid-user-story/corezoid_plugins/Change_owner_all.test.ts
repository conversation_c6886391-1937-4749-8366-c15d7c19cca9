import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { application } from '../../../../application/Application';
import { User } from '../../../../infrastructure/model/User';
import { createAuthUser, Method } from '../../../../utils/request';
import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { axiosInstance } from '../../../../application/api/AxiosClient';

describe('Change owner_all_obj', () => {
  let company_id: any;
  let superUser: User;
  let cookieSUser: any;
  let user1: User;
  let apiUser1: ApiUserClient;
  let user2: User;
  let apiUser2: ApiUserClient;

  beforeAll(async () => {
    superUser = await application.getAuthorizedUser({ company: {} }, 1);
    cookieSUser = createAuthUser(superUser.cookieUser, 'cookie');
    user1 = await application.getAuthorizedUser({ company: {} }, 8);
    apiUser1 = await application.getApiUserClient(user1);
    company_id = '92e51b4a-d67e-41e0-8c7a-d8f9d10e44a0';
    user2 = await application.getAuthorizedUser({ company: {} }, 9);
    apiUser2 = await application.getApiUserClient(user2);
  });

  const ownershipTransfers = [
    {
      description: 'user2 -> user1',
      oldOwnerLoginType: 'corezoid',
      newOwnerLoginType: 'google',
      getOldOwner: (): User => user2,
      getNewOwner: (): User => user1,
      getApiClient: (): ApiUserClient => apiUser1,
      getExpectedOwnerId: (): number => user1.id,
    },
    {
      description: 'user1 -> user2',
      oldOwnerLoginType: 'google',
      newOwnerLoginType: 'corezoid',
      getOldOwner: (): User => user1,
      getNewOwner: (): User => user2,
      getApiClient: (): ApiUserClient => apiUser2,
      getExpectedOwnerId: (): number => user2.id,
    },
  ];

  describe.each(ownershipTransfers)(
    'Change owner_all_obj $description',
    ({ getOldOwner, oldOwnerLoginType, getNewOwner, newOwnerLoginType, getApiClient, getExpectedOwnerId }) => {
      test('Change owner_all_obj', async () => {
        const responseConf = await axiosInstance({
          method: 'GET',
          url: `${ConfigurationManager.getConfiguration().getUrl()}system/conf`,
        });
        expect(responseConf.status).toBe(200);

        const responseChangeOwner2 = await cookieSUser.request({
          method: Method.POST,
          url: `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/plugins/system/change_owner`,
          data: {
            old_owner_login: getOldOwner().email,
            old_owner_login_type: oldOwnerLoginType,
            new_owner_login: getNewOwner().email,
            new_owner_login_type: newOwnerLoginType,
          },
        });
        expect(responseChangeOwner2.status).toBe(200);
        expect((responseChangeOwner2.data as Array<any>).every(item => item.owner_login === getNewOwner().email)).toBe(
          true,
        );

        await new Promise(r => setTimeout(r, 5000));

        const responseListFolder = await getApiClient().request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id,
            sort: 'date',
            order: 'desc',
          }),
        );
        expect(responseListFolder.status).toBe(200);
        expect(responseListFolder.body.ops[0].proc).toEqual('ok');
        expect(
          (responseListFolder.body.ops[0].list as Array<any>).find(item =>
            ['folder', 'dashboard', 'conv'].includes(item.obj_type),
          )?.owner_id,
        ).toEqual(getExpectedOwnerId());

        const responseListProjects = await getApiClient().request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.PROJECTS,
            obj_id: 0,
            company_id,
            sort: 'title',
            order: 'asc',
          }),
        );
        expect(responseListProjects.status).toBe(200);
        expect(responseListProjects.body.ops[0].proc).toEqual('ok');
        expect(
          (responseListProjects.body.ops[0].list as Array<any>).find(item => item.obj_type === 'project'),
        ).toMatchObject({
          owner_id: getExpectedOwnerId(),
          title: 'New Project',
        });

        const responseListAliases = await getApiClient().request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.ALIASES,
            company_id,
            sort: 'title',
            order: 'asc',
          }),
        );
        expect(responseListAliases.status).toBe(200);
        expect(responseListAliases.body.ops[0].proc).toEqual('ok');
        expect(
          (responseListAliases.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias'),
        ).toMatchObject({
          owner_id: getExpectedOwnerId(),
          title: 'testFolderAlias',
        });

        const responseListAPIkey = await getApiClient().request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.COMPANY_USERS,
            company_id,
            sort: 'title',
            order: 'asc',
            filter: 'api_key',
          }),
        );
        expect(responseListAPIkey.status).toBe(200);
        expect(responseListAPIkey.body.ops[0].proc).toEqual('ok');
        expect((responseListAPIkey.body.ops[0].list as Array<any>).find(item => item.obj_id === 133462)).toMatchObject({
          owner_id: getExpectedOwnerId(),
          title: 'testKey',
        });

        const responseListGroup = await getApiClient().request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.COMPANY_USERS,
            company_id,
            sort: 'title',
            order: 'asc',
            filter: 'group',
          }),
        );
        expect(responseListGroup.status).toBe(200);
        expect(responseListGroup.body.ops[0].proc).toEqual('ok');
        expect((responseListGroup.body.ops[0].list as Array<any>).find(item => item.obj_id === 382599)).toMatchObject({
          owner_id: getExpectedOwnerId(),
          title: 'testGroup',
        });
      });
    },
  );
});
