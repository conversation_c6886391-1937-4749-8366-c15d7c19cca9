import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { requestDeleteObj } from '../../../../application/api/ApiObj';
import { axiosInstance } from '../../../../application/api/AxiosClient';

describe('Corezoid Plugins (positive)', () => {
  let company_id: any;
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let conv_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test(`DirectURL API 2`, async () => {
    const responseCreateConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        title: `Back-end Test DirectURL`,
        status: 'active',
      }),
    );
    conv_id = responseCreateConv.body.ops[0].obj_id;

    const responseModifyConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id,
      }),
    );
    expect(responseModifyConv.status).toBe(200);
    expect(responseModifyConv.body.request_proc).toBe('ok');
    const callback_hash = responseModifyConv.body.ops[0].callback_hash;

    const url = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${conv_id}/${callback_hash}`;
    const responseJSON = await axiosInstance({
      method: 'POST',
      url,
      data: {
        companyID: '*********',
        id: '${__RandomString(30,qwertyuiopasdfghjklzxcvbnm0123456789,)}',
        withoutCommission: false,
        head: {
          lang: 'ru',
          pointType: 'WKASS',
          bank: 'PB',
          search: {
            property: [
              {
                alias: 'BILL_MULTIPLE_CHOICE_PROPERTY',
                val: '251106:8',
              },
            ],
          },
        },
        point: {
          user: 'jjjjjj',
          branch: 'DNH0',
        },
        client: {
          identType: 'EKB',
          ident: '**********',
          identification: {
            surname: 'Артем',
          },
        },
        systemID: 'CNEW',
        service: [
          {
            id: 'JHDSJHFJHEWIUIU23I4324KJRJFSFJ',
            property: [
              {
                alias: 'COMPANY',
                val: 'Единая',
                'xsi:type': 'SimpleProperty',
              },
              {
                value: {
                  counter: [
                    {
                      deltaValue: 6,
                      currValue: 7,
                      prevValue: 1,
                      number: '5545454',
                    },
                    {
                      deltaValue: 4,
                      currValue: 5,
                      prevValue: 1,
                      number: '6565656',
                    },
                  ],
                },
                alias: 'COUNTERS',
                'xsi:type': 'CountersProperty',
              },
              {
                value: {
                  value: 'г. Кировоград',
                  esaID: '*********',
                },
                alias: 'ADDRESS',
                'xsi:type': 'AddressProperty',
              },
              {
                alias: 'DEST',
                val: 'Оплата',
                'xsi:type': 'SimpleProperty',
              },
              {
                value: {
                  begin: {
                    day: '1',
                    month: '99',
                    year: '9999',
                  },
                  end: {
                    day: '31',
                    month: '5',
                    year: '2016',
                  },
                },
                alias: 'PERIOD',
                'xsi:type': 'PeriodProperty',
              },
              {
                alias: 'MFO',
                val: '************9583',
                'xsi:type': 'SimpleProperty',
              },
              {
                alias: 'LS',
                val: '************9999999',
                'xsi:type': 'SimpleProperty',
              },
              {
                alias: 'ACCOUNT',
                val: '************99999999',
                'xsi:type': 'SimpleProperty',
              },
              {
                alias: 'SUM',
                val: '0.01',
                'xsi:type': 'SumProperty',
              },
              {
                alias: 'CUSTOM_COMPANY',
                val: 'Днипро',
                'xsi:type': 'SimpleProperty',
              },
              {
                alias: 'FIO',
                val: 'ХАРІНА',
                'xsi:type': 'SimpleProperty',
              },
              {
                value: {
                  val: 'Iншi надходження',
                  key: '32',
                },
                alias: 'KASS_SYMB',
                'xsi:type': 'ListProperty',
              },
            ],
            regularRequest: {
              phone: '',
              fromDebt: true,
              sum: 0,
              periodicity: 'M',
              writeOfDate: 25,
              cardNumber: '************99999',
              altcardNumber: '************99999',
              paymType: 'C',
              paylimit: '0.00',
              workPeriod: {
                begin: {
                  day: '25',
                  month: '99',
                  year: '9999',
                },
                end: {
                  day: '10',
                  month: '10',
                  year: '2513',
                },
              },
              cardExp: {
                day: 1,
                month: 4,
                year: 2017,
              },
              phoneoper: '',
              email: '<EMAIL>',
            },
            requisitePay: {
              okpo: '************99999',
              name: 'Скороходов Артем Александрович',
              company: 'Единая Квитанция Кировоград',
              requisiteServ: {
                mfo: '************',
                account: '************999',
                destination: 'Оплата',
              },
            },
          },
        ],
      },
    });
    expect(responseJSON.status).toBe(200);
    expect(responseJSON.data.ops.proc).toEqual('ok');

        const url = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${conv_id}/${callback_hash}`;
    const responseJSON = await axiosInstance({
      method: 'POST',
      url,
      data: {
        companyID: '*********',
        id: '${__RandomString(30,qwertyuiopasdfghjklzxcvbnm0123456789,)}',
        withoutCommission: false,
        head: {
          lang: 'ru',
          pointType: 'WKASS',
          bank: 'PB',
          search: {
            property: [
              {
                alias: 'BILL_MULTIPLE_CHOICE_PROPERTY',
                val: '251106:8',
              },
            ],
          },
        },
        point: {
          user: 'jjjjjj',
          branch: 'DNH0',
        },
        client: {
          identType: 'EKB',
          ident: '**********',
          identification: {
            surname: 'Артем',
          },
        },
        systemID: 'CNEW',
        service: [
          {
            id: 'JHDSJHFJHEWIUIU23I4324KJRJFSFJ',
            property: [
              {
                alias: 'COMPANY',
                val: 'Единая',
                'xsi:type': 'SimpleProperty',
              },
              {
                value: {
                  counter: [
                    {
                      deltaValue: 6,
                      currValue: 7,
                      prevValue: 1,
                      number: '5545454',
                    },
                    {
                      deltaValue: 4,
                      currValue: 5,
                      prevValue: 1,
                      number: '6565656',
                    },
                  ],
                },
                alias: 'COUNTERS',
                'xsi:type': 'CountersProperty',
              },
              {
                value: {
                  value: 'г. Кировоград',
                  esaID: '*********',
                },
                alias: 'ADDRESS',
                'xsi:type': 'AddressProperty',
              },
              {
                alias: 'DEST',
                val: 'Оплата',
                'xsi:type': 'SimpleProperty',
              },
              {
                value: {
                  begin: {
                    day: '1',
                    month: '99',
                    year: '9999',
                  },
                  end: {
                    day: '31',
                    month: '5',
                    year: '2016',
                  },
                },
                alias: 'PERIOD',
                'xsi:type': 'PeriodProperty',
              },
              {
                alias: 'MFO',
                val: '************9583',
                'xsi:type': 'SimpleProperty',
              },
              {
                alias: 'LS',
                val: '************9999999',
                'xsi:type': 'SimpleProperty',
              },
              {
                alias: 'ACCOUNT',
                val: '************99999999',
                'xsi:type': 'SimpleProperty',
              },
              {
                alias: 'SUM',
                val: '0.01',
                'xsi:type': 'SumProperty',
              },
              {
                alias: 'CUSTOM_COMPANY',
                val: 'Днипро',
                'xsi:type': 'SimpleProperty',
              },
              {
                alias: 'FIO',
                val: 'ХАРІНА',
                'xsi:type': 'SimpleProperty',
              },
              {
                value: {
                  val: 'Iншi надходження',
                  key: '32',
                },
                alias: 'KASS_SYMB',
                'xsi:type': 'ListProperty',
              },
            ],
            regularRequest: {
              phone: '',
              fromDebt: true,
              sum: 0,
              periodicity: 'M',
              writeOfDate: 25,
              cardNumber: '************99999',
              altcardNumber: '************99999',
              paymType: 'C',
              paylimit: '0.00',
              workPeriod: {
                begin: {
                  day: '25',
                  month: '99',
                  year: '9999',
                },
                end: {
                  day: '10',
                  month: '10',
                  year: '2513',
                },
              },
              cardExp: {
                day: 1,
                month: 4,
                year: 2017,
              },
              phoneoper: '',
              email: '<EMAIL>',
            },
            requisitePay: {
              okpo: '************99999',
              name: 'Скороходов Артем Александрович',
              company: 'Единая Квитанция Кировоград',
              requisiteServ: {
                mfo: '************',
                account: '************999',
                destination: 'Оплата',
              },
            },
          },
        ],
      },
    });
    expect(responseJSON.status).toBe(200);
    expect(responseJSON.data.ops.proc).toEqual('ok');
  });

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseConv.status).toBe(200);
  });
});
