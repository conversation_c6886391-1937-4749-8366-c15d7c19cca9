import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { requestDeleteObj } from '../../../../application/api/ApiObj';

describe('Corezoid Plugins (positive)', () => {
  let company_id: any;
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test(`Messengers`, async () => {
    const responseCreateConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        title: `Back-end Test Messengers`,
        status: 'active',
      }),
    );
    newConv = responseCreateConv.body.ops[0].obj_id;

    const responseModifyConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(responseModifyConv.status).toBe(200);
    expect(responseModifyConv.body.request_proc).toBe('ok');

    const responseCreateChannelFB = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        conv_id: newConv,
        company_id,
        folder_id: 0,
        obj_type: 'fbmessenger',
        token: 'asjdljkljl1j3lu21ydh,jasndkl',
      }),
    );
    expect(responseCreateChannelFB.status).toBe(200);
    expect(responseCreateChannelFB.body.ops[0].proc).toBe('ok');
    expect(responseCreateChannelFB.body.ops[0].obj).toBe('channel');
    expect(responseCreateChannelFB.body.ops[0].obj_type).toBe('fbmessenger');

    const responseGetCallbackHash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        conv_id: newConv,
        company_id,
      }),
    );
    const callback_hash_1 = responseGetCallbackHash.body.ops[0].callback_hash;
    expect(responseGetCallbackHash.status).toBe(200);
    expect(responseGetCallbackHash.body.request_proc).toBe('ok');

    const responseCreateChannelTelegram = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        conv_id: newConv,
        company_id,
        folder_id: 0,
        obj_type: 'telegram',
        token: '*********************************************',
        callback_hash: callback_hash_1,
      }),
    );
    expect(responseCreateChannelTelegram.status).toBe(200);
    expect(responseCreateChannelTelegram.body.ops[0].proc).toBe('ok');
    expect(responseCreateChannelTelegram.body.ops[0].obj).toBe('channel');
    expect(responseCreateChannelTelegram.body.ops[0].obj_type).toBe('telegram');

    await new Promise(r => setTimeout(r, 5000));

    const responseCreateChannelSlack = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        conv_id: newConv,
        company_id,
        folder_id: 0,
        obj_type: 'slack',
        env: 'front',
      }),
    );
    expect(responseCreateChannelSlack.status).toBe(200);
    expect(responseCreateChannelSlack.body.ops[0].proc).toBe('ok');
    expect(responseCreateChannelSlack.body.ops[0].obj).toBe('channel');
    expect(responseCreateChannelSlack.body.ops[0].obj_type).toBe('slack');
  });

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseConv.status).toBe(200);
  });
});
