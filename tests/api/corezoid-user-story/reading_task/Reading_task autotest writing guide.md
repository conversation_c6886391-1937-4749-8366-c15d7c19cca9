# Пошаговое описание тестовых сценариев для реализации в проекте corezoid-automation-playwright

## Перед всеми тестами (beforeAll)
beforeAll(async () => {
  Create_conveyor_A
  Create_State_B
  Check_procces_node_id_conveyor_A
  Check_procces_node_id_state_B
  await pause(10000)
  Create_Logic_Set_param_A
});

## Основные тесты (test)

1. No_Privs
test('No_Privs', async () => {
  Send_task_to_conv
  await pause(15000)
  Check_API_COPY_result_No_Privs (403 Forbidden)
});

2. With_Privs (reading parameter task)
test('With_Privs (reading parameter task)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Send_task_to_state
  3. Send_task_to_conv
  await pause(15000)
  4. Check_API_COPY_result (200 OK)
});

3. With_Privs (reading parameter task_dinamic_string/dinamic_ref_2part)
test('With_Privs (reading parameter task_dinamic_string/dinamic_ref_2part)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Send_task_to_state
  3. Modify_Logic_Set_param_A (reading parameter task/dinamic_ref_2part_witch"_")
  4. Send_task_to_conv
  await pause(15000)
  5. Check_API_COPY_result
  6. Modify_Logic_Set_param_A (reading parameter task/dinamic_ref_2part)
  7. Send_task_to_conv
  await pause(15000)
  8. Check_API_COPY_result
  9. Modify_Logic_Set_param_A (reading parameter task/dinamic_ref_3part)
  10. Send_task_to_conv
  await pause(15000)
  11. Check_API_COPY_result
});

4. With_Privs (reading parameter task_dinamic_string)
test('With_Privs (reading parameter task_dinamic_string)', async () => {
  1. Link_conv_B_to Api_A (view)
  2. Modify_Logic_Set_param_A (reading parameter task_dinamic_string)
  3. Send_task_to_conv
  await pause(10000)
  4. Check_API_COPY_result
});

5. With_Privs (reading parameter task_dinamic_object)
test('With_Privs (reading parameter task_dinamic_object)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Send_task_to_state
  3. Modify_Logic_Set_param_A (reading parameter task_dinamic_object)
  4. Send_task_to_conv
  await pause(15000)
  5. Check_API_COPY_result (200 OK)
});

6. With_Privs (reading parameter task_dinamic_array)
test('With_Privs (reading parameter task_dinamic_array)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Send_task_to_state
  3. Modify_Logic_Set_param_A (reading parameter task_dinamic_array)
  4. Send_task_to_conv
  await pause(15000)
  5. Check_API_COPY_result (200 OK)
});

7. With_Privs (reading_dinamic_conv/ref/key)
test('With_Privs (reading_dinamic_conv/ref/key)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Send_task_to_state
  3. Modify_Logic_Set_param_A (reading_dinamic_conv/ref/key)
  4. Send_task_to_conv
  await pause(10000)
  5. Check_API_COPY_result
});

8. With_Privs (reading all task)
test('With_Privs (reading all task)', async () => {
  1. Modify_Logic_Set_param_A (reading all task)
  2. Send_task_to_conv
  await pause(10000)
  3. Check_API_COPY_result
});

9. With_Privs (reading all task dinamic)
test('With_Privs (reading all task dinamic)', async () => {
  1. Modify_Logic_Set_param_A
  2. Send_task_to_conv
  await pause(15000)
  3. Check_API_COPY_result
});

10. With_Privs (reading parameter object in task)
test('With_Privs (reading parameter object in task)', async () => {
  1. Modify_Logic_Set_param_A (первая вложенность строка)
  2. Send_task_to_conv
  await pause(10000)
  3. Check_API_COPY_result
  4. Modify_Logic_Set_param_A (первая вложенность объект)
  5. Send_task_to_conv
  await pause(10000)
  6. Check_API_COPY_result
  7. Modify_Logic_Set_param_A (вторая вложенность)
  8. Send_task_to_conv
  await pause(10000)
  9. Check_API_COPY_result
  10. Modify_Logic_Set_param_A (вторая вложенность)
  11. Send_task_to_conv
  await pause(10000)
  12. Check_API_COPY_result
  13. Modify_Logic_Set_param_A (ключ с индексом)
  14. Send_task_to_conv
  await pause(10000)
  15. Check_API_COPY_result
  16. Modify_Logic_Set_param_A_dinamic_key2
  17. Send_task_to_conv
  await pause(10000)
  18. Check_API_COPY_result
});

11. With_Privs (reading parameter array in task)
test('With_Privs (reading parameter array in task)', async () => {
  1. Modify_Logic_Set_param_A (первая вложенность строка)
  2. Send_task_to_conv
  await pause(10000)
  3. Check_API_COPY_result
  4. Modify_Logic_Set_param_A (ключ с индексом)
  5. Send_task_to_conv
  await pause(10000)
  6. Check_API_COPY_result
  7. Modify_Logic_Set_param_A (вторая вложенность массив)
  8. Send_task_to_conv
  await pause(10000)
  9. Check_API_COPY_result
  10. Modify_Logic_Set_param_A (первая вложенность объект)
  11. Send_task_to_conv
  await pause(10000)
  12. Check_API_COPY_result
  13. Modify_Logic_Set_param_A (вторая вложенность объект)
  14. Send_task_to_conv
  await pause(10000)
  15. Check_API_COPY_result
  16. Modify_Logic_Set_param_A_dinamic_key1/key2
  17. Send_task_to_conv
  await pause(10000)
  18. Check_API_COPY_result
});

12. With_Privs (reading_parameter_obj_dinamic_conv/ref/key)
test('With_Privs (reading_parameter_obj_dinamic_conv/ref/key)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Send_task_to_state
  3. Modify_Logic_Set_param_A (reading_parameter_obj_dinamic_conv/ref/key)
  4. Send_task_to_conv
  await pause(10000)
  5. Check_API_COPY_result
});

13. With_Privs (reading_parameter_obj_dinamic_conv/ref/key/key1)
test('With_Privs (reading_parameter_obj_dinamic_conv/ref/key/key1)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Modify_Logic_Set_param_A (reading_parameter_obj_dinamic_conv/ref/key/key1)
  3. Send_task_to_conv
  await pause(15000)
  4. Check_API_COPY_result (200 OK)
  5. Modify_Logic_Set_param_A (reading_parameter_obj_dinamic_conv/ref/key/key2)
  6. Send_task_to_conv
  await pause(15000)
  7. Check_API_COPY_result (200 OK)
});

14. With_Privs (reading_parameter_array_dinamic_conv/ref/key/key1)
test('With_Privs (reading_parameter_array_dinamic_conv/ref/key/key1)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Modify_Logic_Set_param_A (reading_parameter_array_dinamic_conv/ref/key/key1)
  3. Send_task_to_conv
  await pause(15000)
  4. Check_API_COPY_result (200 OK)
  5. Modify_Logic_Set_param_A (reading_parameter_array_dinamic_conv/ref/key/key2)
  6. Send_task_to_conv
  await pause(15000)
  7. Check_API_COPY_result (200 OK)
});

15. With_Privs (reading parameter task_dinamic_string Constr AND)
test('With_Privs (reading parameter task_dinamic_string Constr AND)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Modify_Logic_Set_param_A (reading parameter task_dinamic_string)
  3. Send_task_to_conv
  await pause(15000)
  4. Check_API_COPY_result (200 OK)
});

16. With_Privs (reading parameter task_dinamic_string ConstrАttached)
test('With_Privs (reading parameter task_dinamic_string ConstrАttached)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Modify_Logic_Set_param_A (reading parameter task_dinamic_string)
  3. Send_task_to_conv
  await pause(15000)
  4. Check_API_COPY_result (200 OK)
});

17. Nonexistent_conv/state
test('Nonexistent_conv/state', async () => {
  1. Modify_Logic_Set_param_A Nonexistent_conv/state
  2. Link_conv_B_to_Api_A (view)
  2. Send_task_to_conv
  await pause(15000)
  4. Check_API_COPY_result (200 OK)
  5. Modify_Logic_Set_param_A 
});

18. Construrtion_in_key
test('Construrtion_in_key', async () => {
  1. Modify_Logic_Set_param_A_construrtion_in_key
  2. Link_conv_B_to_Api_A (view)
  2. Send_task_to_conv
  await pause(15000)
  4. Check_API_COPY_result (200 OK)
  5. Modify_Logic_Set_param_A 
});

19. With_Privs (reading parameter task)(вычитываемые символы после запятой)
test('With_Privs (reading parameter task)(вычитываемые символы после запятой)', async () => {
  1. Link_conv_B_to_Api_A (view)
  2. Send_task_to_state
  3. Modify_Logic_Set_param_A (reading parameter task)
  4. Send_task_to_conv
  await pause(15000)
  5. Check_API_COPY_result (200 OK)
});

## После всех тестов (afterAll)
afterAll(async () => {
  Delete_state_B
  await pause(20000)
  Check_API_COPY_result (With_Privs delete state)
  Delete_conveyor_A
  Destroy_conveyor_A
  Destroy_state_B
});

---

> **Задача для Devin:** реализовать эти `test()`-блоки, используя существующие HTTP-утилиты проекта и паузы между шагами.
> 
> **Важно:**
> - Для каждого теста вместо комментариев нужно использовать реальные тела запросов, **извлечённые из JMeter-файла `Reading_constr.jmx`**. 
> - Например, JMX содержит строку:
>   ````
>   {"type":"create","obj":"conv","title":"Logic_Set_param","description":"Barabashka","status":"active","company_id":""+company+""}
>   ````
>   В коде это должно быть:
>   ```ts
>   const responseConv = await api.request(
>     createRequestWithOps({
>       type: REQUEST_TYPE.CREATE,
>       obj: OBJ_TYPE.CONV,
>       title: 'Logic_Set_param',
>       description: 'Barabashka',
>       status: 'active',
>       company_id,
>     }),
>   );
>   ```
> - То есть, **ключи и значения** в объекте запроса должны соответствовать `testname` и параметрам, указанным в соответствующих samplers JMX. 
> - Проверь все samplers в контроллере и корректно преобразуй их JSON-тела в вызовы `createRequestWithOps`.
