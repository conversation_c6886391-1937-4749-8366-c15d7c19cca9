import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { application } from '../../../../application/Application';
import { requestListConv, requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import { NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';
import { v4 as uuidv4 } from 'uuid';

describe('Reading Task JMeter Migration', () => {
  let api: ApiUserClient;
  let apiB: ApiKeyClient;
  let company_id: any;
  let conveyorA: string;
  let stateB: string;
  let apiBKeyId: string | number;
  let user_id: string | number;
  let process_nodeA_ID: string;
  let final_nodeA_ID: string;
  let task_id: string | number;
  const uuid = uuidv4();

  beforeAll(
    async (): Promise<void> => {
      const user = await application.getAuthorizedUser();
      api = await application.getApiUserClient(user);
      const apikey = await application.getApiKey();
      company_id = apikey.companies[0].id;
      user_id = user.id;

      const CreateKeyResponse = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          title: 'apiUser',
          logins: [{ type: 'api' }],
          company_id,
        }),
      );
      expect(CreateKeyResponse.status).toBe(200);
      apiBKeyId = CreateKeyResponse.body.ops[0].users[0].obj_id;
      const apiKey = CreateKeyResponse.body.ops[0].users[0].logins[0].obj_id;
      const apiSecret = CreateKeyResponse.body.ops[0].users[0].logins[0].key;
      apiB = new ApiKeyClient({
        key: apiKey,
        secret: apiSecret,
        id: apiBKeyId.toString(),
        companies: [{ id: company_id }],
      });

      const responseConveyorA = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CONV,
          title: 'Logic_Set_param',
          description: 'Barabashka',
          status: 'active',
          company_id,
        }),
      );
      expect(responseConveyorA.status).toBe(200);
      conveyorA = responseConveyorA.body.ops[0].obj_id;

      const responseStateB = await apiB.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CONV,
          title: 'State',
          description: 'Barabashka',
          status: 'active',
          conv_type: 'state',
          company_id,
        }),
      );
      expect(responseStateB.status).toBe(200);
      stateB = responseStateB.body.ops[0].obj_id;

      const Check_procces_node_id_conveyor_A = await requestListConv(api, conveyorA, company_id);
      expect(Check_procces_node_id_conveyor_A.status).toBe(200);
      process_nodeA_ID = (Check_procces_node_id_conveyor_A.body.ops[0].list as Array<any>).find(
        item => item.title === 'process',
      ).obj_id;
      final_nodeA_ID = (Check_procces_node_id_conveyor_A.body.ops[0].list as Array<any>).find(
        item => item.title === 'final',
      ).obj_id;

      const Check_procces_node_id_state_B = await requestListConv(apiB, stateB, company_id);
      expect(Check_procces_node_id_state_B.status).toBe(RESP_STATUS.OK);

      const Create_Logic_Set_param_A = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_nodeA_ID,
          conv_id: conveyorA,
          title: 'process',
          obj_type: 0,
          logics: [
            {
              type: NODE_LOGIC_TYPE.SetParam,
              extra: { test: `{{conv[${stateB}].ref[test].key}}` },
              extra_type: { test: 'string' },
              err_node_id: '',
            },
            { to_node_id: final_nodeA_ID, type: 'go', node_title: 'final' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(Create_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);
      expect(Create_Logic_Set_param_A.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(api, conveyorA, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    },
  );

  test('No_Privs', async (): Promise<void> => {
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: uuid,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    expect(Send_task_to_conv.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const Check_API_COPY_result_No_privs = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result_No_privs.status).toBe(RESP_STATUS.OK);
    const { obj, obj_id, data } = Check_API_COPY_result_No_privs.body.ops[0];
    expect(obj).toEqual('task');
    expect(obj_id).toEqual(task_id);
    expect(data.__conveyor_set_param_return_type_tag__).toEqual('access_denied');
  });

  test('With_Privs (reading parameter task)', async (): Promise<void> => {
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          conv: stateB,
          object: {
            a: 'ee',
            b: [
              {
                b1: 'ss',
              },
            ],
            c: {
              c1: 'dd',
            },
          },
          arrey: [
            {
              a1: [
                {
                  a2: 'qq',
                },
              ],
            },
            { a1: 'ww' },
            { a1: { a2: 'gg' } },
          ],
        },
        ref: `test`,
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: uuid,
        },
        ref: `conv_task_ref_${Date.now()}`,
      }),
    );
    expect(Send_task_to_conv.status).toBe(200);
    expect(Send_task_to_conv.body.ops[0].proc).toBe('ok');
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    const check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    const { obj, obj_id, data } = check_API_COPY_result.body.ops[0];
    expect(obj).toEqual('task');
    expect(obj_id).toEqual(task_id);
    expect(data.test).toEqual('12345');
  });

  test('With_Privs (reading parameter task_dinamic_string/dinamic_ref_2part)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
        },
        ref: 'test_12345',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A (reading parameter task/dinamic_ref_2part_with"_")
    const Modify_Logic_Set_param_A_2part_with_underscore = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[{{param1}}_{{param2}}].{{id}}}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A_2part_with_underscore.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref1 = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv_1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'key',
          param1: 'test',
          param2: '12345',
        },
        ref: ref1,
      }),
    );
    expect(Send_task_to_conv_1.status).toBe(RESP_STATUS.OK);
    const task_id_1 = Send_task_to_conv_1.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result_1 = await requestShow(api, OBJ_TYPE.TASK, task_id_1, { conv_id: conveyorA });
    expect(Check_API_COPY_result_1.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result_1.body.ops[0].data.id).toEqual('key');
    expect(Check_API_COPY_result_1.body.ops[0].data.param1).toEqual('test');
    expect(Check_API_COPY_result_1.body.ops[0].data.param2).toEqual('12345');
    expect(Check_API_COPY_result_1.body.ops[0].data.test).toEqual('12345');

    // Step 6: Modify_Logic_Set_param_A (reading parameter task/dinamic_ref_2part)
    const Modify_Logic_Set_param_A_2part = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[{{param1}}{{param2}}].{{id}}}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A_2part.status).toBe(RESP_STATUS.OK);

    // Step 7: Send_task_to_conv
    const ref2 = `ref_${Date.now() + 1}`;
    const Send_task_to_conv_2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'key',
          param1: 'test_',
          param2: '12345',
        },
        ref: ref2,
      }),
    );
    expect(Send_task_to_conv_2.status).toBe(RESP_STATUS.OK);
    const task_id_2 = Send_task_to_conv_2.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 8: Check_API_COPY_result
    const Check_API_COPY_result_2 = await requestShow(api, OBJ_TYPE.TASK, task_id_2, { conv_id: conveyorA });
    expect(Check_API_COPY_result_2.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result_2.body.ops[0].data.id).toEqual('key');
    expect(Check_API_COPY_result_2.body.ops[0].data.param1).toEqual('test_');
    expect(Check_API_COPY_result_2.body.ops[0].data.param2).toEqual('12345');
    expect(Check_API_COPY_result_2.body.ops[0].data.test).toEqual('12345');

    // Step 9: Modify_Logic_Set_param_A (reading parameter task/dinamic_ref_3part)
    const Modify_Logic_Set_param_A_3part = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[{{param1}}{{param2}}{{param3}}].key}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A_3part.status).toBe(RESP_STATUS.OK);

    // Step 10: Send_task_to_conv
    const ref3 = `ref_${Date.now() + 2}`;
    const Send_task_to_conv_3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'k',
          id1: 'y',
          param1: 'test_',
          param2: '123',
          param3: '45',
        },
        ref: ref3,
      }),
    );
    expect(Send_task_to_conv_3.status).toBe(RESP_STATUS.OK);
    const task_id_3 = Send_task_to_conv_3.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 11: Check_API_COPY_result
    const Check_API_COPY_result_3 = await requestShow(api, OBJ_TYPE.TASK, task_id_3, { conv_id: conveyorA });
    expect(Check_API_COPY_result_3.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result_3.body.ops[0].data.id).toEqual('k');
    expect(Check_API_COPY_result_3.body.ops[0].data.id1).toEqual('y');
    expect(Check_API_COPY_result_3.body.ops[0].data.param1).toEqual('test_');
    expect(Check_API_COPY_result_3.body.ops[0].data.param2).toEqual('123');
    expect(Check_API_COPY_result_3.body.ops[0].data.param3).toEqual('45');
    expect(Check_API_COPY_result_3.body.ops[0].data.test).toEqual('12345');
  });

  test('With_Privs (reading parameter task_dinamic_string)', async (): Promise<void> => {
    // Сначала создаем задачу в state с ref="test" и нужными данными
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: '12345',
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 1: Link_conv_B_to Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Modify_Logic_Set_param_A (reading parameter task_dinamic_string)
    const Modify_Logic_Set_param_A_dinamic = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test].{{id}}}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A_dinamic.status).toBe(RESP_STATUS.OK);

    // Step 3: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'key',
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 4: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.id).toEqual('key');
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual('12345'); // результат логики {{conv[stateB].ref[test].{{id}}}}
  });

  test('With_Privs (reading parameter task_dinamic_object)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state (создаем задачу с объектом в state B)
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          object: {
            a: 'ee',
            b: [{ b1: 'ss' }],
            c: { c1: 'dd' },
          },
          arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A (reading parameter task_dinamic_object)
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test].{{id}}}}`,
            },
            extra_type: {
              test: 'object',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'object',
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.id).toEqual('object');
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual({
      a: 'ee',
      b: [{ b1: 'ss' }],
      c: { c1: 'dd' },
    });
  });

  test('With_Privs (reading parameter task_dinamic_array)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state (создаем задачу с массивом в state B)
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          object: {
            a: 'ee',
            b: [{ b1: 'ss' }],
            c: { c1: 'dd' },
          },
          arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A (reading parameter task_dinamic_array)
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test].{{id}}}}`,
            },
            extra_type: {
              test: 'array',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'arrey',
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.id).toEqual('arrey');
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual([
      { a1: [{ a2: 'qq' }] },
      { a1: 'ww' },
      { a1: { a2: 'gg' } },
    ]);
  });

  test('With_Privs (reading_dinamic_conv/ref/key)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          conv: stateB,
          object: {
            a: 'ee',
            b: [{ b1: 'ss' }],
            c: { c1: 'dd' },
          },
          arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[{{conv}}].ref[{{ref}}].{{id}}}}`,
            },
            extra_type: {
              test: 'array',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'arrey',
          ref: 'test',
          conv: stateB,
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.id).toEqual('arrey');
    expect(Check_API_COPY_result.body.ops[0].data.ref).toEqual('test');
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual([
      { a1: [{ a2: 'qq' }] },
      { a1: 'ww' },
      { a1: { a2: 'gg' } },
    ]);
  });

  test('With_Privs (reading all task)', async (): Promise<void> => {
    // Step 1: Modify_Logic_Set_param_A (reading all task)
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test]}}`,
            },
            extra_type: {
              test: 'object',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 3: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual({
      key: 12345,
      new: 'qqq',
      conv: stateB,
      object: {
        a: 'ee',
        b: [{ b1: 'ss' }],
        c: { c1: 'dd' },
      },
      arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
    });
  });

  test('With_Privs (reading all task dinamic)', async (): Promise<void> => {
    // Step 1: Modify_Logic_Set_param_A (reading all task dinamic)
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[{{conv}}].ref[{{ref}}]}}`,
            },
            extra_type: {
              test: 'object',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          conv: stateB,
          ref: 'test',
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.conv).toEqual(stateB);
    expect(Check_API_COPY_result.body.ops[0].data.ref).toEqual('test');
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual({
      key: 12345,
      new: 'qqq',
      conv: stateB,
      object: {
        a: 'ee',
        b: [{ b1: 'ss' }],
        c: { c1: 'dd' },
      },
      arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
    });
  });

  test('With_Privs (reading parameter object in task)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          object: {
            a: 'ee',
            b: [{ b1: 'ss' }],
            c: { c1: 'dd' },
          },
          arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test].object.a}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual('ee');
  });

  test('With_Privs (reading parameter array in task)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          object: {
            a: 'ee',
            b: [{ b1: 'ss' }],
            c: { c1: 'dd' },
          },
          arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test].arrey[0].a1[0].a2}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual('qq');
  });

  // test.skip('With_Privs (reading_parameter_obj_dinamic_conv/ref/key)', async (): Promise<void> => {
  //   const linkResponse = await api.request(
  //     createRequestWithOps({
  //       type: REQUEST_TYPE.LINK,
  //       obj: OBJ_TYPE.CONV,
  //       obj_id: stateB,
  //       company_id,
  //       obj_to: 'user',
  //       obj_to_id: apiBKeyId,
  //       privs: [{ type: 'view', list_obj: ['all'] }],
  //     }),
  //   );
  //   expect(linkResponse.status).toBe(200);

  //   const sendTaskToStateResponse = await api.request(
  //     createRequestWithOps({
  //       type: REQUEST_TYPE.CREATE,
  //       obj: OBJ_TYPE.TASK,
  //       conv_id: stateB,
  //       data: {
  //         dynamic_object: {
  //           key1: 'value1',
  //           key2: 'value2',
  //         },
  //       },
  //       ref: `state_task_obj_dynamic_${Date.now()}`,
  //     }),
  //   );
  //   expect(sendTaskToStateResponse.status).toBe(200);

  //   const modifyLogicResponse = await api.request(
  //     createRequestWithOps({
  //       type: REQUEST_TYPE.MODIFY,
  //       obj: OBJ_TYPE.NODE,
  //       obj_id: logicSetParamA,
  //       conv_id: conveyorA,
  //       extra: {
  //         obj: 'set_param',
  //         param: 'result',
  //         value: '{{conv[' + stateB + '].ref["state_task_obj_dynamic_' + Date.now() + '"].data.dynamic_object}}',
  //       },
  //       company_id,
  //     }),
  //   );
  //   expect(modifyLogicResponse.status).toBe(200);

  //   const sendTaskToConvResponse = await apiB.request(
  //     createRequestWithOps({
  //       type: REQUEST_TYPE.CREATE,
  //       obj: OBJ_TYPE.TASK,
  //       conv_id: conveyorA,
  //       data: {
  //         key1: 'value1',
  //         key2: 'value2',
  //       },
  //       ref: `conv_task_obj_dynamic_${Date.now()}`,
  //     }),
  //   );

  //   await new Promise(resolve => setTimeout(resolve, 10000));

  //   expect(sendTaskToConvResponse.status).toBe(200);
  //   expect(sendTaskToConvResponse.body.ops[0].proc).toBe('ok');
  // });

  test('With_Privs (reading_parameter_obj_dinamic_conv/ref/key/key1)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          object: {
            a: 'ee',
            b: [{ b1: 'ss' }],
            c: { c1: 'dd' },
          },
          arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test].{{id}}.a}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'object',
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.id).toEqual('object');
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual('ee');
  });

  test('With_Privs (reading_parameter_array_dinamic_conv/ref/key/key1)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Modify_Logic_Set_param_A (reading_parameter_array_dinamic_conv/ref/key/key1)
    const Modify_Logic_Set_param_A_1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[{{conv}}].ref[{{ref}}].arrey[0].{{key1}}}}`,
            },
            extra_type: {
              test: 'object',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A_1.status).toBe(RESP_STATUS.OK);

    // Step 3: Send_task_to_conv
    const ref1 = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv_1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          key1: 'a1',
          ref: 'test',
          conv: stateB,
        },
        ref: ref1,
      }),
    );
    expect(Send_task_to_conv_1.status).toBe(RESP_STATUS.OK);
    const task_id_1 = Send_task_to_conv_1.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 4: Check_API_COPY_result
    const Check_API_COPY_result_1 = await requestShow(api, OBJ_TYPE.TASK, task_id_1, { conv_id: conveyorA });
    expect(Check_API_COPY_result_1.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result_1.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result_1.body.ops[0].data.test).toEqual([{ a2: 'qq' }]);

    // Step 5: Modify_Logic_Set_param_A (reading_parameter_array_dinamic_conv/ref/key/key2)
    const Modify_Logic_Set_param_A_2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[{{conv}}].ref[{{ref}}].arrey[2].{{key1}}.{{key2}}}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A_2.status).toBe(RESP_STATUS.OK);

    // Step 6: Send_task_to_conv
    const ref2 = `ref_${Date.now() + 1}`;
    const Send_task_to_conv_2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          key1: 'a1',
          key2: 'a2',
          ref: 'test',
          conv: stateB,
        },
        ref: ref2,
      }),
    );
    expect(Send_task_to_conv_2.status).toBe(RESP_STATUS.OK);
    const task_id_2 = Send_task_to_conv_2.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 7: Check_API_COPY_result
    const Check_API_COPY_result_2 = await requestShow(api, OBJ_TYPE.TASK, task_id_2, { conv_id: conveyorA });
    expect(Check_API_COPY_result_2.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result_2.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result_2.body.ops[0].data.test).toEqual('gg');
  });

  test('With_Privs (reading parameter task_dinamic_string Constr AND)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          object: {
            a: 'ee',
            b: [{ b1: 'ss' }],
            c: { c1: 'dd' },
          },
          arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test].{{id1}}}} AND {{conv[${stateB}].ref[test].{{id2}}}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id1: 'key',
          id2: 'new',
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.id1).toEqual('key');
    expect(Check_API_COPY_result.body.ops[0].data.id2).toEqual('new');
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual('12345 AND qqq');
  });

  test('With_Privs (reading parameter task_dinamic_string ConstrАttached)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          object: {
            a: 'ee',
            b: [{ b1: 'ss' }],
            c: { c1: 'dd' },
          },
          arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `prefix_{{conv[${stateB}].ref[test].{{id}}}}_suffix`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'new',
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.id).toEqual('new');
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual('prefix_qqq_suffix');
  });

  test('Nonexistent_conv/state', async (): Promise<void> => {
    // Step 1: Modify_Logic_Set_param_A (with nonexistent conv reference)
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: '{{conv[999999].task.data.nonexistent_key}}',
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 3: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual('');
  });

  test('Construrtion_in_key', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345,
          new: 'qqq',
          object: {
            a: 'ee',
            b: [{ b1: 'ss' }],
            c: { c1: 'dd' },
          },
          arrey: [{ a1: [{ a2: 'qq' }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test].key}}_construction`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
          id: 'key',
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.id).toEqual('key');
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual('12345_construction');
  });

  test('With_Privs (reading parameter task)(вычитываемые символы после запятой)', async (): Promise<void> => {
    // Step 1: Link_conv_B_to_Api_A (view)
    const Link_conv_B_to_Api_A = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: stateB,
        company_id,
        obj_to: 'user',
        obj_to_id: user_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(Link_conv_B_to_Api_A.status).toBe(RESP_STATUS.OK);

    // Step 2: Send_task_to_state
    const Send_task_to_state = await apiB.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: stateB,
        data: {
          key: 12345.5675676767,
          conv: stateB,
          array: [{ a1: [{ a2: 234234.34223423 }] }, { a1: 'ww' }, { a1: { a2: 'gg' } }],
        },
        ref: 'test1',
      }),
    );
    expect(Send_task_to_state.status).toBe(RESP_STATUS.OK);

    // Step 3: Modify_Logic_Set_param_A
    const Modify_Logic_Set_param_A = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_nodeA_ID,
        conv_id: conveyorA,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: {
              test: `{{conv[${stateB}].ref[test1].key}},{{conv[${stateB}].ref[test1].array[0].a1[0].a2}}`,
            },
            extra_type: {
              test: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_nodeA_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
      }),
    );
    expect(Modify_Logic_Set_param_A.status).toBe(RESP_STATUS.OK);

    // Step 4: Send_task_to_conv
    const ref = `ref_${Date.now()}`;
    const rnd = uuid;
    const Send_task_to_conv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conveyorA,
        data: {
          test1: rnd,
        },
        ref: ref,
      }),
    );
    expect(Send_task_to_conv.status).toBe(RESP_STATUS.OK);
    task_id = Send_task_to_conv.body.ops[0].obj_id;

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Check_API_COPY_result
    const Check_API_COPY_result = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conveyorA });
    expect(Check_API_COPY_result.status).toBe(RESP_STATUS.OK);
    expect(Check_API_COPY_result.body.ops[0].data.test1).toEqual(rnd);
    expect(Check_API_COPY_result.body.ops[0].data.test).toEqual('12345.568,234234.342');
  });

  afterAll(
    async (): Promise<void> => {
      const responseDeleteStateB = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CONV,
          obj_id: stateB,
          company_id,
        }),
      );
      expect(responseDeleteStateB.status).toBe(200);

      await new Promise(resolve => setTimeout(resolve, 20000));

      const responseDeleteConveyorA = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CONV,
          obj_id: conveyorA,
          company_id,
        }),
      );
      expect(responseDeleteConveyorA.status).toBe(200);

      const responseDeleteApiKey = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: apiBKeyId,
          company_id,
        }),
      );
      expect(responseDeleteApiKey.status).toBe(200);
    },
  );
});
