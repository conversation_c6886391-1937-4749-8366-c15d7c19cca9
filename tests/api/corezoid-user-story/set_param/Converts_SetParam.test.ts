import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestListConv, requestCreateObj, requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import { NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';

describe('SetParam - Type conversion tests', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: string | number;
  let process_node_ID: string;
  let final_node_ID: string;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseCreateConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SetParam`);
    conv_id = responseCreateConv.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  // Common test data constants
  const COMMON_OBJECT_DATA = {
    Pascal: [
      { Name: 'Pascal Made Simple', price: 700 },
      { Name: 'Guide to Pascal', price: 400 },
    ],
    Scala: [
      { Name: 'Scala for the Impatient', price: 1000 },
      { Name: 'Scala in Depth', price: 1300 },
    ],
  };

  const COMMON_BASE_DATA = {
    xxx: 'industrial',
    obj: [1354, 12, 984],
    num: 77777,
  };

  const COMMON_EXPECTED_BASE = {
    obj: [1354, 12, 984],
    num: 77777,
    xxx: 'industrial',
  };

  const conversionTestData = [
    {
      description: 'Convert object to string',
      convParam: { name: 'object', descr: 'object', type: 'string' },
      setParamExtra: { object: '{{object}}' },
      setParamExtraType: { object: 'string' },
      taskData: {
        object: COMMON_OBJECT_DATA,
        ...COMMON_BASE_DATA,
      },
      expectedResults: {
        ...COMMON_EXPECTED_BASE,
        object:
          '{"Pascal":[{"Name":"Pascal Made Simple","price":700},{"Name":"Guide to Pascal","price":400}],"Scala":[{"Name":"Scala for the Impatient","price":1000},{"Name":"Scala in Depth","price":1300}]}',
      },
    },
    {
      description: 'Convert object to array',
      convParam: { name: 'array', descr: 'array', type: 'array' },
      setParamExtra: { array: '{{array}}' },
      setParamExtraType: { array: 'array' },
      taskData: {
        array: [COMMON_OBJECT_DATA],
        ...COMMON_BASE_DATA,
      },
      expectedResults: {
        ...COMMON_EXPECTED_BASE,
        'array[0].Pascal': COMMON_OBJECT_DATA.Pascal,
        'array[0].Scala': COMMON_OBJECT_DATA.Scala,
      },
    },
    {
      description: 'Convert array to object',
      convParam: { name: 'objectAR', descr: 'objectAR', type: 'string' },
      setParamExtra: { objectAR: '{{objectAR}}' },
      setParamExtraType: { objectAR: 'object' },
      taskData: {
        objectAR: COMMON_OBJECT_DATA,
        ...COMMON_BASE_DATA,
      },
      expectedResults: {
        ...COMMON_EXPECTED_BASE,
        objectAR: COMMON_OBJECT_DATA,
      },
    },
    {
      description: 'Convert string to number',
      convParam: { name: 'number', descr: 'number', type: 'number' },
      setParamExtra: { number: '{{string}}' },
      setParamExtraType: { number: 'number' },
      taskData: {
        string: '555.24',
        ...COMMON_BASE_DATA,
      },
      expectedResults: {
        ...COMMON_EXPECTED_BASE,
        number: 555.24,
      },
    },
    {
      description: 'Convert number to boolean',
      convParam: [
        { name: 'boolean_num0', type: 'boolean' },
        { name: 'boolean_num1', type: 'boolean' },
      ],
      setParamExtra: { boolean0: '{{boolean_num0}}', boolean1: '{{boolean_num1}}' },
      setParamExtraType: { boolean0: 'boolean', boolean1: 'boolean' },
      taskData: {
        boolean_num0: 0,
        boolean_num1: 1,
      },
      expectedResults: {
        boolean0: false,
        boolean1: true,
      },
    },
    {
      description: 'Hash functions (sha256, md5, etc.)',
      convParam: { name: 'boolean_num', type: 'boolean' },
      setParamExtra: {
        md5: '$.md5_hex(test)',
        sha256: '$.sha256_hex(test)',
        sha224: '$.sha224_hex(test)',
        sha384: '$.sha384_hex(test)',
        sha512B: '$.base64_encode($.sha512(test))',
        sha1: '$.sha1_hex({{key}})',
      },
      setParamExtraType: {
        md5: 'string',
        sha256: 'string',
        sha224: 'string',
        sha384: 'string',
        sha512B: 'string',
        sha1: 'string',
      },
      taskData: {
        key: 'test',
        boolean_num: 1,
        ...COMMON_BASE_DATA,
      },
      expectedResults: {
        ...COMMON_EXPECTED_BASE,
        md5: '098f6bcd4621d373cade4e832627b4f6',
        sha224: '90a3ed9e32b2aaf4c61c410eb925426119e1a9dc53d4286ade99a809',
        sha384: '768412320f7b0aa5812fce428dc4706b3cae50e02a64caa16a782249bfe8efc4b7ef1ccb126255d196047dfedf17a0a9',
        sha512B: '7iaw3Ur350mqGo7jwQrpkj9hiYB3Lkc/iBml1JQODbJ6wYX4oOHV+E+IvIh/1nsUNzLDBMxfqa2Ob1f1ACio/w==',
        sha256: '9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08',
        sha1: 'a94a8fe5ccb19ba61c4c0873d391e987982fbbd3',
      },
    },
  ];

  describe.each(conversionTestData)(
    '$description',
    ({ convParam, setParamExtra, setParamExtraType, taskData, expectedResults }): void => {
      test('should convert correctly', async () => {
        const responseConvParam = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.CONV_PARAMS,
            obj_id: conv_id,
            params: Array.isArray(convParam)
              ? convParam.map(p => ({ ...p, flags: [], regex: '', regex_error_text: '' }))
              : [{ ...convParam, flags: [], regex: '', regex_error_text: '' }],
          }),
        );
        expect(responseConvParam.status).toBe(RESP_STATUS.OK);

        const modifySetParam = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID,
            conv_id: conv_id,
            title: 'process',
            obj_type: 0,
            logics: [
              {
                type: NODE_LOGIC_TYPE.SetParam,
                extra: setParamExtra,
                extra_type: setParamExtraType,
                err_node_id: '',
              },
              { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(modifySetParam.status).toBe(RESP_STATUS.OK);
        expect(modifySetParam.body.ops[0].proc).toEqual(PROC_STATUS.OK);

        const responseCommit = await requestConfirm(api, conv_id, company_id);
        expect(responseCommit.status).toBe(RESP_STATUS.OK);
        expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: conv_id,
            data: taskData,
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(RESP_STATUS.OK);
        expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
        task_id = responseTask.body.ops[0].obj_id;

        await new Promise(r => setTimeout(r, 3000));

        const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
        expect(responseShowTask.status).toBe(RESP_STATUS.OK);
        const { obj, obj_id, data } = responseShowTask.body.ops[0];
        expect(obj).toEqual('task');
        expect(obj_id).toEqual(task_id);

        Object.keys(expectedResults).forEach(key => {
          if (key.includes('[') && key.includes(']')) {
            const [arrayName, property] = key.split('.');
            const [name, index] = arrayName.split('[');
            const idx = parseInt(index.replace(']', ''));
            expect(data[name][idx][property]).toEqual((expectedResults as any)[key]);
          } else {
            expect(data[key]).toEqual((expectedResults as any)[key]);
          }
        });

        const responseList = await requestListConv(api, conv_id, company_id);
        expect(responseList.status).toBe(RESP_STATUS.OK);
        const { proc, status } = responseList.body.ops[0];
        expect(proc).toEqual(PROC_STATUS.OK);
        expect(status).toEqual('active');

        const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODE,
            obj_id: final_node_ID,
            conv_id,
            company_id,
            limit: 10,
          }),
        );
        expect(response.status).toBe(RESP_STATUS.OK);
        expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
        expect((response.body.ops[0].list as Array<any>).some(item => item.obj_id === task_id)).toBe(true);
      });
    },
  );

  test('String to Number Error', async () => {
    const responseConvParam = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: conv_id,
        params: [{ name: 'number', descr: 'number', type: 'number', flags: [], regex: '', regex_error_text: '' }],
      }),
    );
    expect(responseConvParam.status).toBe(RESP_STATUS.OK);

    const modifySetParam = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: { number: '{{string}}' },
            extra_type: { number: 'number' },
            err_node_id: '',
          },
          { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(modifySetParam.status).toBe(RESP_STATUS.OK);
    expect(modifySetParam.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          string: [1354, 12, 984],
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    const { obj, obj_id, data } = responseShowTask.body.ops[0];
    expect(obj).toEqual('task');
    expect(obj_id).toEqual(task_id);
    expect(data.__conveyor_set_param_return_description__).toEqual(
      'Param: number, Value: [1354,12,984], Try convert to: number',
    );
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
