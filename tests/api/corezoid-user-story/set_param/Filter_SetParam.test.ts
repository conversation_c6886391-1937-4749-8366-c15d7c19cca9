import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestListConv, requestCreateObj, requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import { NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';

describe('SetParam - Filter operations with different type predicates', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: string | number;
  let process_node_ID: string;
  let final_node_ID: string;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SetParam`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  const filterOperationTestData = [
    {
      description: 'Filter with Erlang type predicates - selecting elements by data type validation',
      mapFunctions: {
        is_integerString: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), is_integer(Test) end, {{b2}})`,
        is_integerArray: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), is_integer(Test) end, {{b2}})`,
        is_binaryString: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), is_binary(Test) end, {{b2}})`,
        is_binaryObject: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), is_binary(Test) end, {{b2}})`,
        is_listString: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), is_list(Test) end, {{b2}})`,
        is_listArray: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), is_list(Test) end, {{b2}})`,
      },
      extraType: {
        is_integerString: 'string',
        is_integerArray: 'array',
        is_binaryString: 'string',
        is_binaryObject: 'object',
        is_listString: 'string',
        is_listArray: 'array',
      },
      testData: {
        b2: [{ test: '3FF' }, { test: 30 }, { test: [{ a: 1 }] }, { test: 30.3 }, { test: true }],
      },
      expectedResults: {
        is_integerString: '[{"test":30}]',
        is_integerArray: [{ test: 30 }],
        is_binaryString: '[{"test":"3FF"}]',
        is_binaryObject: [{ test: '3FF' }],
        is_listString: '[{"test":[{"a":1}]}]',
        is_listArray: [{ test: [{ a: 1 }] }],
      },
    },
    {
      description: 'Filter with eutils functions - number type validation using is_number predicate',
      mapFunctions: {
        eutilsString: `$.filter(fun(Item) -> Test = eutils:get_value(<<"test">>, Item), is_number(Test) end, {{b11}})`,
        eutilsArray: `$.filter(fun(Item) -> Test = eutils:get_value(<<"test">>, Item), is_number(Test) end, {{b11}})`,
      },
      extraType: {
        eutilsString: 'string',
        eutilsArray: 'array',
      },
      testData: {
        b11: [{ test: 'wer' }, { test: 1 }],
      },
      expectedResults: {
        eutilsString: '[{"test":1}]',
        eutilsArray: [{ test: 1 }],
      },
    },
    {
      description: 'Filter with binary operations and conditions - string replacement with conditional filtering',
      mapFunctions: {
        replacefilterString: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"replace">>, binary:replace(Test, <<"ba">>,<<"123">>)} | Item], <<"replace">> /= <<"12345">> end, {{c}})`,
        replacefilterArray: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"replace">>, binary:replace(Test, <<"ba">>,<<"123">>)} | Item], <<"replace">> /= <<"12345">> end, {{c}})`,
        splitfilterString: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"split">>, Test} | Item], <<"split">> /= <<"12345">> end, {{c1}})`,
        splitfilterArray: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"split">>, Test} | Item], <<"split">> /= <<"12345">> end, {{c1}})`,
      },
      extraType: {
        replacefilterString: 'string',
        replacefilterArray: 'array',
        splitfilterString: 'string',
        splitfilterArray: 'array',
      },
      testData: {
        c: [{ test: 'ba45' }],
        c1: [{ test: 'ba_45' }],
      },
      expectedResults: {
        replacefilterString: '[{"test":"ba45"}]',
        replacefilterArray: [{ test: 'ba45' }],
        splitfilterString: '[{"test":"ba_45"}]',
        splitfilterArray: [{ test: 'ba_45' }],
      },
    },
    {
      description: 'Filter with arithmetic operations and comparisons - equality, modulo, and property exclusion',
      mapFunctions: {
        filterEqualString: `$.filter(fun(Item) -> Item == 2 end, {{a}})`,
        filterEqualArray: `$.filter(fun(Item) -> Item == 2 end, {{a}})`,
        filterRemString: `$.filter(fun(Item) when Item rem 2 < 1 -> true; (_) -> false end, {{a}})`,
        filterRemArray: `$.filter(fun(Item) when Item rem 2 < 1 -> true; (_) -> false end, {{a}})`,
        filterProplistsString: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item) =/= 5 end, {{a1}})`,
        filterProplistsArray: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item) =/= 5 end, {{a1}})`,
      },
      extraType: {
        filterEqualString: 'string',
        filterEqualArray: 'array',
        filterRemString: 'string',
        filterRemArray: 'array',
        filterProplistsString: 'string',
        filterProplistsArray: 'array',
      },
      testData: {
        a: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],
        a1: [
          { test: 1 },
          { test: 2 },
          { test: 3 },
          { test: 4 },
          { test: 5 },
          { test: 6 },
          { test: 7 },
          { test: 8 },
          { test: 9 },
          { test: 10 },
          { test: 11 },
          { test: 12 },
        ],
      },
      expectedResults: {
        filterEqualString: '[2]',
        filterEqualArray: [2],
        filterRemString: '[2,4,6,8,10,12,14]',
        filterRemArray: [2, 4, 6, 8, 10, 12, 14],
        filterProplistsString:
          '[{"test":1},{"test":2},{"test":3},{"test":4},{"test":6},{"test":7},{"test":8},{"test":9},{"test":10},{"test":11},{"test":12}]',
        filterProplistsArray: [
          { test: 1 },
          { test: 2 },
          { test: 3 },
          { test: 4 },
          { test: 6 },
          { test: 7 },
          { test: 8 },
          { test: 9 },
          { test: 10 },
          { test: 11 },
          { test: 12 },
        ],
      },
    },
  ];

  describe.each(filterOperationTestData)(
    '$description',
    ({ mapFunctions, testData, expectedResults, extraType }): void => {
      test('should execute filter operations correctly', async () => {
        const modifySetParam = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID,
            conv_id: conv_id,
            title: 'process',
            obj_type: 0,
            logics: [
              {
                type: NODE_LOGIC_TYPE.SetParam,
                extra: mapFunctions,
                extra_type: extraType,
                err_node_id: '',
              },
              { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(modifySetParam.status).toBe(RESP_STATUS.OK);
        expect(modifySetParam.body.ops[0].proc).toEqual(PROC_STATUS.OK);

        const responseCommit = await requestConfirm(api, conv_id, company_id);
        expect(responseCommit.status).toBe(RESP_STATUS.OK);
        expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: conv_id,
            data: testData,
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(RESP_STATUS.OK);
        expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
        task_id = responseTask.body.ops[0].obj_id;

        await new Promise(r => setTimeout(r, 3000));

        const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
        expect(responseShowTask.status).toBe(RESP_STATUS.OK);
        const { obj, obj_id, data } = responseShowTask.body.ops[0];
        expect(obj).toEqual('task');
        expect(obj_id).toEqual(task_id);

        Object.entries(expectedResults).forEach(([key, expected]) => {
          expect(data[key]).toEqual(expected);
        });

        const responseListNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODE,
            obj_id: final_node_ID,
            conv_id,
            company_id,
            limit: 10,
          }),
        );
        expect(responseListNode.status).toBe(RESP_STATUS.OK);
        const { proc, list } = responseListNode.body.ops[0];
        expect(proc).toBe(PROC_STATUS.OK);
        expect((list as Array<any>).some(item => item.obj_id === task_id)).toBe(true);
      });
    },
  );

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
