import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestListConv, requestCreateObj, requestConfirm } from '../../../../application/api/ApiObj';
import { NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';

describe('SetParam', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: string | number;
  let process_node_ID: string;
  let final_node_ID: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SetParam`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  test('list node filter', async () => {
    const modifySetParam = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: NODE_LOGIC_TYPE.SetParam,
            extra: { a: 'new', b: 'test' },
            extra_type: { a: 'string', b: 'string' },
            err_node_id: '',
          },
          { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(modifySetParam.status).toBe(RESP_STATUS.OK);
    expect(modifySetParam.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    for (let i = 1; i < 4; i++) {
      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id: conv_id,
          data: {
            list: `${i}`,
          },
          ref: `referens`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      await new Promise(r => setTimeout(r, 3000));
    }

    const responseListNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        conv_id,
        company_id,
        limit: 10,
        filters: [
          {
            name: 'ref',
            value: 'referens',
            fun: 'eq',
          },
        ],
      }),
    );
    expect(responseListNode.status).toBe(RESP_STATUS.OK);
    expect(responseListNode.body.ops[0].proc).toBe(PROC_STATUS.OK);
    const items = (responseListNode.body.ops[0].list as Array<any>).filter(item => item.ref === 'referens');

    items.forEach(item => {
      expect(item.data.a).toBe('new');
      expect(item.data.b).toBe('test');
    });
    const lists = items.map(item => item.data.list).sort();
    expect(lists).toEqual(['1', '2', '3']);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
