import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestListConv, requestCreateObj, requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import { NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';

describe('SetParam - Map operations with different operators', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: string | number;
  let process_node_ID: string;
  let final_node_ID: string;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SetParam`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  const mapOperationTestData = [
    {
      description: 'Map with comparison operators (==, <) - evaluating numeric equality and less than conditions',
      mapFunctions: {
        a: `$.map(fun(Item) -> Item == 2 end, {{test}})`,
        b: `$.map(fun(Item) -> Item < 2 end, {{test}})`,
      },
      testData: { test: [1, 2, 3] },
      expectedResults: {
        a: '[false,true,false]',
        b: '[true,false,false]',
      },
    },
    {
      description: 'Map with extended logical operators (xor, andalso, orelse) - advanced boolean operations',
      mapFunctions: {
        a: `$.map(fun(Item) -> Item xor true end, {{test}})`,
        b: `$.map(fun(Item) -> Item andalso true end, {{test}})`,
        c: `$.map(fun(Item) -> Item orelse true end, {{test}})`,
      },
      testData: { test: [true, false] },
      expectedResults: {
        a: '[false,true]',
        b: '[true,false]',
        c: '[true,true]',
      },
    },
    {
      description: 'Map with basic logical operators (and, or, not) - fundamental boolean operations',
      mapFunctions: {
        a: `$.map(fun(Item) -> Item and true end, {{test}})`,
        b: `$.map(fun(Item) -> Item or true end, {{test}})`,
        c: `$.map(fun(Item) -> not false end, {{test}})`,
      },
      testData: { test: [true, false] },
      expectedResults: {
        a: '[true,false]',
        b: '[true,true]',
        c: '[true,true]',
      },
    },
    {
      description: 'Map with arithmetic operators (div, rem, +) - mathematical calculations on numeric data',
      mapFunctions: {
        a: `$.map(fun(Item) -> Item div 3 end, {{test}})`,
        b: `$.map(fun(Item) -> Item rem 3 end, {{test}})`,
        c: `$.map(fun(Item) -> Item +1 end, {{test}})`,
      },
      testData: { test: [1, 2, 3] },
      expectedResults: {
        a: '[0,0,1]',
        b: '[1,2,0]',
        c: '[2,3,4]',
      },
    },
    {
      description: 'Map with bitwise operators (band, bsl) - binary operations with different output types',
      mapFunctions: {
        a: `$.map(fun(Item) -> Item band 2 end, {{test}})`,
        b: `$.map(fun(Item) -> Item band 2 end, {{test}})`,
        c: `$.map(fun(Item) -> Item bsl 2 end, {{test}})`,
        d: `$.map(fun(Item) -> Item bsl 2 end, {{test}})`,
      },
      extraType: {
        b: 'array',
        d: 'array',
      },
      testData: { test: [1, 2, 3] },
      expectedResults: {
        a: '[0,2,2]',
        b: [0, 2, 2],
        c: '[4,8,12]',
        d: [4, 8, 12],
      },
    },
    {
      description: 'Map with list concatenation (++) - adding new properties to objects',
      mapFunctions: {
        a: `$.map(fun(Item) -> Item ++ [{<<"map">>, 1}] end, {{test}})`,
        b: `$.map(fun(Item) -> Item ++ [{<<"map">>, 1}] end, {{test}})`,
      },
      extraType: {
        b: 'array',
      },
      testData: { test: [{ test: 1 }, { test1: 2 }] },
      expectedResults: {
        a: '[{"test":1,"map":1},{"test1":2,"map":1}]',
        b: [
          { test: 1, map: 1 },
          { test1: 2, map: 1 },
        ],
      },
    },
    {
      description: 'Map with eutils functions - JSON parsing and value extraction utilities',
      mapFunctions: {
        a: `$.map(fun(Item) -> Test = eutils:get_value(<<"test">>, Item) end, {{b11}})`,
        b: `$.map(fun(Item) -> Test0 = eutils:get_value(<<"test">>, Item), Test1 = eutils:from_json(Test0), [{<<"a">>, eutils:get_value(<<"a">>, Test1)} | Item]  end, {{b0}})`,
      },
      extraType: {
        b: 'array',
      },
      testData: {
        b11: [{ test: 'wer' }, { test: 'wer1' }],
        b0: [{ test: '{"a":1}' }, { test: '{"a":2}' }],
      },
      expectedResults: {
        a: '["wer","wer1"]',
        b: [
          { a: 1, test: '{"a":1}' },
          { a: 2, test: '{"a":2}' },
        ],
      },
    },
    {
      description: 'Map with Erlang built-in functions - binary_to_float and list tail operations',
      mapFunctions: {
        binarytofloat: `$.map(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"binarytofloat">>, erlang:binary_to_float(Test)} | Item] end, {{b1}})`,
        erlangtl: `$.map(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"erlangtl">>, erlang:tl(Test)} | Item] end, {{b4}})`,
      },
      extraType: {
        erlangtl: 'array',
      },
      testData: {
        b1: [{ test: '10.1' }, { test: '2.3452543' }],
        b4: [{ test: [1, 2, 3, 3, 4, 6] }, { test: [2, 10, 34] }],
      },
      expectedResults: {
        binarytofloat: '[{"binarytofloat":10.1,"test":"10.1"},{"binarytofloat":2.3452543,"test":"2.3452543"}]',
        erlangtl: [
          { erlangtl: [2, 3, 3, 4, 6], test: [1, 2, 3, 3, 4, 6] },
          { erlangtl: [10, 34], test: [2, 10, 34] },
        ],
      },
    },
    {
      description: 'Map with base64 decoding - converting encoded strings to readable format',
      mapFunctions: {
        mapbase64decodeS: `$.map(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"base64">>, base64:decode(Test)} | Item] end, {{b1}})`,
        mapbase64decodeA: `$.map(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"base64">>, base64:decode(Test)} | Item] end, {{b1}})`,
      },
      extraType: {
        mapbase64decodeA: 'array',
      },
      testData: {
        b1: [{ test: 'ZmFsc2U=' }, { test: 'dHJ1ZQ==' }],
      },
      expectedResults: {
        mapbase64decodeS: '[{"base64":"false","test":"ZmFsc2U="},{"base64":"true","test":"dHJ1ZQ=="}]',
        mapbase64decodeA: [
          { base64: 'false', test: 'ZmFsc2U=' },
          { base64: 'true', test: 'dHJ1ZQ==' },
        ],
      },
    },
    {
      description: 'Map with binary string operations - text replacement and splitting functionality',
      mapFunctions: {
        replace: `$.map(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"replace">>, binary:replace(Test, <<"ba">>,<<"123">>)} | Item] end, {{c}})`,
        split: `$.map(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), [{<<"split">>, binary:split(Test, <<"_">>)} | Item] end, {{c}})`,
      },
      extraType: {
        replace: 'array',
      },
      testData: {
        c: [{ test: 'ban_ana' }],
      },
      expectedResults: {
        replace: [{ replace: '123n_ana', test: 'ban_ana' }],
        split: '[{"split":["ban","ana"],"test":"ban_ana"}]',
      },
    },
    {
      description: 'Filter with Erlang type predicates - selecting elements by data type validation',
      mapFunctions: {
        is_integer: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), is_integer(Test) end, {{b2}})`,
        is_binary: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), is_binary(Test) end, {{b2}})`,
        is_list: `$.filter(fun(Item) -> Test = proplists:get_value(<<"test">>, Item), is_list(Test) end, {{b2}})`,
      },
      extraType: {
        is_integer: 'array',
        is_binary: 'object',
      },
      testData: {
        b2: [{ test: '3FF' }, { test: 30 }, { test: [{ a: 1 }] }, { test: 30.3 }, { test: true }],
      },
      expectedResults: {
        is_binary: [{ test: '3FF' }],
        is_list: '[{"test":[{"a":1}]}]',
        is_integer: [{ test: 30 }],
      },
    },
  ];

  describe.each(mapOperationTestData)(
    '$description',
    ({ mapFunctions, testData, expectedResults, extraType }): void => {
      test('should execute map operations correctly', async () => {
        const finalExtraType = Object.keys(mapFunctions).reduce((acc, key) => {
          acc[key] = extraType?.[key as keyof typeof extraType] || 'string';
          return acc;
        }, {} as Record<string, string>);

        const modifySetParam = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID,
            conv_id: conv_id,
            title: 'process',
            obj_type: 0,
            logics: [
              {
                type: NODE_LOGIC_TYPE.SetParam,
                extra: mapFunctions,
                extra_type: finalExtraType,
                err_node_id: '',
              },
              { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(modifySetParam.status).toBe(RESP_STATUS.OK);
        expect(modifySetParam.body.ops[0].proc).toEqual(PROC_STATUS.OK);

        const responseCommit = await requestConfirm(api, conv_id, company_id);
        expect(responseCommit.status).toBe(RESP_STATUS.OK);
        expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: conv_id,
            data: testData,
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(RESP_STATUS.OK);
        expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
        task_id = responseTask.body.ops[0].obj_id;

        await new Promise(r => setTimeout(r, 3000));

        const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
        expect(responseShowTask.status).toBe(RESP_STATUS.OK);
        const { obj, obj_id, data } = responseShowTask.body.ops[0];
        expect(obj).toEqual('task');
        expect(obj_id).toEqual(task_id);

        Object.entries(expectedResults).forEach(([key, expected]) => {
          expect(data[key]).toEqual(expected);
        });

        const responseListNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODE,
            obj_id: final_node_ID,
            conv_id,
            company_id,
            limit: 10,
          }),
        );
        expect(responseListNode.status).toBe(RESP_STATUS.OK);
        const { proc, list } = responseListNode.body.ops[0];
        expect(proc).toBe(PROC_STATUS.OK);
        expect((list as Array<any>).some(item => item.obj_id === task_id)).toBe(true);
      });
    },
  );

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
