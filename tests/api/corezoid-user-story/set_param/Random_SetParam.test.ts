import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestListConv, requestCreateObj, requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import { NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';

describe('SetParam - Random operations with different value generators', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: string | number;
  let process_node_ID: string;
  let final_node_ID: string;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SetParam`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  const randomOperationTestData = [
    {
      description: 'Random value generators - basic random, bounded random, and range random',
      mapFunctions: {
        a: '$.random()',
        b: '$.random(3)',
        c: '$.random(5,9)',
      },
      extraType: {
        a: 'string',
        b: 'string',
        c: 'string',
      },
      testData: {
        list: '1',
      },
      expectedResults: {
        a: /^0\./,
        b: /^[1-3]$/,
        c: /^[5-9]$/,
      },
    },
    {
      description: 'Random value generators with dynamic parameters - variable-driven random operations',
      mapFunctions: {
        a: '$.random()',
        b: '$.random({{i}})',
        c: '$.random({{i}},{{j}})',
      },
      extraType: {
        a: 'string',
        b: 'string',
        c: 'string',
      },
      testData: {
        i: '5',
        j: '9',
      },
      expectedResults: {
        a: /^0\./,
        b: /^[1-5]$/,
        c: /^[5-9]$/,
      },
    },
    {
      description: 'Mixed utility functions - math calculations, unix timestamp, and random with variables',
      mapFunctions: {
        a: '$.math({{i}}+{{j}})',
        b: '$.unixtime({{i}})',
        c: '$.random({{j}},{{i}})',
      },
      extraType: {
        a: 'string',
        b: 'string',
        c: 'string',
      },
      testData: {
        i: '5',
        j: '9',
      },
      expectedResults: {
        a: '14',
        b: /.+/,
        c: /^[5-9]$/,
        i: '5',
        j: '9',
      },
    },
    {
      description: 'Random edge cases and negative values - zero, negative bounds, and negative ranges',
      mapFunctions: {
        a: '$.random(0)',
        b: '$.random(-5)',
        c: '$.random({{i}})',
        d: '$.random(-5,-9)',
      },
      extraType: {
        a: 'string',
        b: 'string',
        c: 'string',
        d: 'string',
      },
      testData: {
        i: '5',
      },
      expectedResults: {
        a: '0',
        b: /^-[1-5]$/,
        c: /^[1-5]$/,
        d: /^-[5-9]$/,
        i: '5',
      },
    },
  ];

  describe.each(randomOperationTestData)(
    '$description',
    ({ mapFunctions, testData, expectedResults, extraType }): void => {
      test('should execute random operations correctly', async () => {
        const modifySetParam = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID,
            conv_id: conv_id,
            title: 'process',
            obj_type: 0,
            logics: [
              {
                type: NODE_LOGIC_TYPE.SetParam,
                extra: mapFunctions,
                extra_type: extraType,
                err_node_id: '',
              },
              { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(modifySetParam.status).toBe(RESP_STATUS.OK);
        expect(modifySetParam.body.ops[0].proc).toEqual(PROC_STATUS.OK);

        const responseCommit = await requestConfirm(api, conv_id, company_id);
        expect(responseCommit.status).toBe(RESP_STATUS.OK);
        expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: conv_id,
            data: testData,
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(RESP_STATUS.OK);
        expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
        task_id = responseTask.body.ops[0].obj_id;

        await new Promise(r => setTimeout(r, 3000));

        const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
        expect(responseShowTask.status).toBe(RESP_STATUS.OK);
        const { obj, obj_id, data } = responseShowTask.body.ops[0];
        expect(obj).toEqual('task');
        expect(obj_id).toEqual(task_id);

        Object.entries(expectedResults).forEach(([key, expected]) => {
          const actual = data[key];
          if (expected instanceof RegExp) {
            expect(actual).toMatch(expected);
          } else {
            expect(actual).toEqual(expected);
          }
        });

        const responseListNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODE,
            obj_id: final_node_ID,
            conv_id,
            company_id,
            limit: 10,
          }),
        );
        expect(responseListNode.status).toBe(RESP_STATUS.OK);
        const { proc, list } = responseListNode.body.ops[0];
        expect(proc).toBe(PROC_STATUS.OK);
        expect((list as Array<any>).some(item => item.obj_id === task_id)).toBe(true);
      });
    },
  );

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
