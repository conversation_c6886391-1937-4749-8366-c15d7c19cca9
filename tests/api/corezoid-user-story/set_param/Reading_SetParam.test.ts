import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestListConv, requestCreateObj, requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import { NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';

describe('SetParam - Reading data with different access patterns', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: string | number;
  let process_node_ID: string;
  let final_node_ID: string;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SetParam`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  const testData = [
    {
      description: 'Access array element with static index',
      extra: { b: '{{list[0].data[0].product}}', c: 'test' },
      extraType: { b: 'string', c: 'string' },
      taskData: { list: [{ data: [{ product: 1 }] }] },
      expectedResult: { b: '1', c: 'test' },
    },
    {
      description: 'Access array element with dynamic variable index (i=0)',
      extra: { b: '{{list[{{i}}].data[{{i}}].product}}', c: 'test' },
      extraType: { b: 'string', c: 'string' },
      taskData: { list: [{ data: [{ product: 1 }] }], i: 0 },
      expectedResult: { b: '1', c: 'test' },
    },
    {
      description: 'Access nested array element at specific index (i=1)',
      extra: { b: '{{list[{{i}}].data[{{i}}].product}}', c: 'test' },
      extraType: { b: 'string', c: 'string' },
      taskData: { list: [{ data: [{ product: 1 }] }, { data: [{ product: 2 }, { product: 3 }] }], i: 1 },
      expectedResult: { b: '3', c: 'test', i: 1 },
    },
    {
      description: 'Handle out-of-bounds array access gracefully (i=2)',
      extra: { b: '{{list[{{i}}].data[{{i}}].product}}', c: 'test' },
      extraType: { b: 'string', c: 'string' },
      taskData: { list: [{ data: [{ product: 1 }] }, { data: [{ product: 2 }, { product: 3 }] }], i: 2 },
      expectedResult: { b: '', c: 'test', i: 2 },
    },
    {
      description: 'Handle deeply out-of-bounds array access (i=3)',
      extra: { b: '{{list[{{i}}].data[{{i}}].product}}', c: 'test' },
      extraType: { b: 'string', c: 'string' },
      taskData: { list: [{ data: [{ product: 1 }] }, { data: [{ product: 2 }, { product: 3 }] }], i: 3 },
      expectedResult: { b: '', c: 'test', i: 3 },
    },
    {
      description: 'Access nested object property through array',
      extra: { b: '{{list[0].data.product[0].key}}' },
      extraType: { b: 'string' },
      taskData: { list: [{ data: { product: [{ key: 1 }] } }] },
      expectedResult: { b: '1' },
    },
    {
      description: 'Access nested object without array notation',
      extra: { b: '{{list.data.product.key}}' },
      extraType: { b: 'string' },
      taskData: { list: { data: { product: { key: 1 } } } },
      expectedResult: { b: '1' },
    },
    {
      description: 'Access deeply nested object properties',
      extra: { b: '{{list[0].data[0].product.key[0].param.test}}' },
      extraType: { b: 'string' },
      taskData: { list: [{ data: [{ product: { key: [{ param: { test: 1 } }] } }] }] },
      expectedResult: { b: '1' },
    },
    {
      description: 'Return nested object as JSON string',
      extra: { b: '{{list[0].data[0].product.key[0].param}}' },
      extraType: { b: 'string' },
      taskData: { list: [{ data: [{ product: { key: [{ param: { test: '1' } }] } }] }] },
      expectedResult: { b: '{"test":"1"}' },
    },
    {
      description: 'Return nested object as object type',
      extra: { b: '{{list[0].data[0].product}}' },
      extraType: { b: 'object' },
      taskData: { list: [{ data: [{ product: { key: [{ param: { test: '1' } }] } }] }] },
      expectedResult: { b: { key: [{ param: { test: '1' } }] } },
    },
    {
      description: 'Dynamic property name substitution',
      extra: { b: '{{list[0].data[0].{{test}}}}' },
      extraType: { b: 'object' },
      taskData: { list: [{ data: [{ product: { key: [{ param: { test: '1' } }] } }] }], test: 'product' },
      expectedResult: { b: { key: [{ param: { test: '1' } }] } },
    },
    {
      description: 'Multi-level dynamic property substitution with arrays',
      extra: { b: '{{list[0].{{test1}}[0].{{test}}}}' },
      extraType: { b: 'object' },
      taskData: {
        list: [{ data: [{ product: { key: [{ param: { test: '1' } }] } }] }],
        test: 'product',
        test1: 'data',
      },
      expectedResult: { b: { key: [{ param: { test: '1' } }] } },
    },
    {
      description: 'Multi-level dynamic property substitution without arrays',
      extra: { b: '{{list[0].{{test1}}.{{test}}}}' },
      extraType: { b: 'object' },
      taskData: { list: [{ data: { product: { key: [{ param: { test: '1' } }] } } }], test: 'product', test1: 'data' },
      expectedResult: { b: { key: [{ param: { test: '1' } }] }, test: 'product', test1: 'data' },
    },
    {
      description: 'Complex nested dynamic property substitution',
      extra: { b: '{{{{test2}}[0].{{test1}}[0].{{test}}}}' },
      extraType: { b: 'object' },
      taskData: {
        list: [{ data: [{ product: { key: [{ param: { test: '1' } }] } }] }],
        test2: 'list',
        test: 'product',
        test1: 'data',
      },
      expectedResult: { b: { key: [{ param: { test: '1' } }] } },
    },
    {
      description: 'Dynamic key assignment as array type',
      extra: { '{{data[2]}}': '{"test":1}' },
      extraType: { '{{data[2]}}': 'array' },
      taskData: { data: [3, 2] },
      expectedResult: { data: [3, 2, [{ test: 1 }]] },
    },
    {
      description: 'Dynamic key assignment as object type',
      extra: { '{{data[2]}}': '{"test":1}' },
      extraType: { '{{data[2]}}': 'object' },
      taskData: { data: [3, 2] },
      expectedResult: { data: [3, 2, { test: 1 }] },
    },
    {
      description: 'Dynamic key assignment as string type',
      extra: { '{{data[2]}}': '{"test":1}' },
      extraType: { '{{data[2]}}': 'string' },
      taskData: { data: [3, 2] },
      expectedResult: { data: [3, 2, '{"test":1}'] },
    },
  ];

  describe.each(testData)('$description', ({ extra, extraType, taskData, expectedResult }): void => {
    test('should execute correctly', async () => {
      const modifySetParam = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id: conv_id,
          title: 'process',
          obj_type: 0,
          logics: [
            {
              type: NODE_LOGIC_TYPE.SetParam,
              extra,
              extra_type: extraType,
              err_node_id: '',
            },
            { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(modifySetParam.status).toBe(RESP_STATUS.OK);
      expect(modifySetParam.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id: conv_id,
          data: taskData,
          ref: `ref_${Date.now()}`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 3000));

      const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
      expect(responseShowTask.status).toBe(RESP_STATUS.OK);
      const { obj, obj_id, data } = responseShowTask.body.ops[0];
      expect(obj).toEqual('task');
      expect(obj_id).toEqual(task_id);

      Object.keys(expectedResult).forEach(key => {
        expect(data[key]).toEqual((expectedResult as any)[key]);
      });
    });
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
