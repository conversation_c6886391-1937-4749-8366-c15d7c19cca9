import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../infrastructure/model/User';
import { application } from '../../../../application/Application';
import { MyApiResponse } from '../../../../utils/requestRetries';
import { createAuthUser, createApiUrls, Method } from '../../../../utils/request';
import { debug, error } from '../../../../support/utils/logger';

jest.setTimeout(900000);

describe('tacts', (): void => {
  let company_id: string;
  let convId: string;
  let convIdCompany: string;
  let host: string;
  let urls: any;
  let urlsSS: any;
  let userId: number;
  let user: User;
  let newCookieSUser: any;
  let newCookieUser: any;
  let userTactsBefore: number;
  let userTactsAfter: number;
  let userTactsAfter2: number;
  const tactsDecreaseMinimum = 100;

  beforeAll(
    async (): Promise<void> => {
      await ConfigurationManager.getConfiguration().initialize();
      const config = ConfigurationManager.getConfiguration();
      host = config.getApiUrl();
      urls = createApiUrls(config.getApiUrl());
      urlsSS = createApiUrls(config.getSSUrl());
      userId = 49790;
    },
  );

  test('should show tacts_info', async (): Promise<void> => {
    user = await application.getAuthorizedUser({ company: {} }, 1);
    newCookieSUser = createAuthUser(user.cookieUser);

    const checkInitialTacts = (response: MyApiResponse): boolean => {
      return response.status === 200 && response.body.tacts;
    };

    const responseTacts = await newCookieSUser.requestWithRetries(
      {
        url: `${host}system/tacts_info`,
        method: Method.POST,
        data: {
          obj: 'user',
          obj_id: userId,
        },
      },
      checkInitialTacts,
      { maxRetries: 10, initialDelay: 500, maxDelay: 1000 },
    );

    expect(responseTacts.status).toBe(200);
    expect(responseTacts.body.obj_type).toBe('user');
    expect(responseTacts.body.obj_id).toBe(userId);
    userTactsBefore = responseTacts.body.tacts;
    debug(`Initial tacts value: ${userTactsBefore}`);
  });

  test('should show tacts_info after create tasks', async (): Promise<void> => {
    const originalTactsValue = userTactsBefore;
    debug(`Starting test with tacts value: ${originalTactsValue}`);

    user = await application.getAuthorizedUser({ company: {} }, 4);
    newCookieUser = createAuthUser(user.cookieUser);

    const responseConv = await newCookieUser.request({
      method: Method.POST,
      url: urls.API_JSON2,
      data: {
        ops: [
          {
            title: 'New Process',
            description: '',
            folder_id: 0,
            obj: 'conv',
            conv_type: 'process',
            type: 'create',
            obj_type: 0,
            status: 'active',
          },
        ],
      },
    });
    expect(responseConv.status).toBe(200);
    expect(responseConv.data.ops[0].obj).toBe('conv');
    convId = responseConv.data.ops[0].obj_id;

    const tasksCount = 34;
    debug(`Creating ${tasksCount} tasks for process ${convId}`);

    for (let i = 0; i < tasksCount; i++) {
      const responseTask = await newCookieUser.request({
        method: Method.POST,
        url: urls.API_JSON2,
        data: {
          ops: [{ type: 'create', obj: 'task', conv_id: convId, data: { x: '1' }, ref: `task_${Date.now()}_${i}` }],
        },
      });
      expect(responseTask.status).toBe(200);

      if (i % 10 === 0 && i > 0) {
        debug(`Created ${i} of ${tasksCount} tasks...`);
      }
    }
    debug(`All ${tasksCount} tasks created successfully`);

    await new Promise(resolve => setTimeout(resolve, 10000));
    debug('Waiting for tacts to decrease...');

    const checkConditions = (response: MyApiResponse): boolean => {
      if (!response || !response.body || !response.body.tacts) {
        debug('Invalid response format:', response);
        return false;
      }

      userTactsAfter = response.body.tacts;
      const tactsDecrease = userTactsBefore - userTactsAfter;

      debug(`Current tacts: ${userTactsAfter}, decrease from initial: ${tactsDecrease}`);

      return response.status === 200 && response.body.obj_id === userId && tactsDecrease >= tactsDecreaseMinimum;
    };

    const response = await newCookieSUser.requestWithRetries(
      {
        url: urls.TACTS_INFO,
        method: Method.POST,
        data: {
          obj: 'user',
          obj_id: userId,
        },
      },
      checkConditions,
      { maxRetries: 160, initialDelay: 3000, maxDelay: 3000 },
      console.log(Date.now()),
    );

    expect(response.status).toBe(200);
    expect(response.body.obj_type).toEqual('user');
    debug(
      `Tacts decreased from ${userTactsBefore} to ${userTactsAfter}, difference: ${userTactsBefore - userTactsAfter}`,
    );
  });

  test('should show tacts_info after create tasks in my_corezoid and company', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 15000));
    debug('Starting second test for tacts in company...');

    user = await application.getAuthorizedUser({ company: {} }, 4);
    newCookieUser = createAuthUser(user.cookieUser);

    const responseСreate = await newCookieUser.request({
      method: Method.POST,
      url: urls.API_JSON2,
      data: {
        ops: [{ type: 'create', obj: 'company', name: 'Tacts', description: 'company', site: 'corezoid.com' }],
      },
    });
    expect(responseСreate.status).toBe(200);
    expect(responseСreate.data.ops[0].proc).toBe('ok');
    company_id = responseСreate.data.ops[0].obj_id;
    debug(`Created company with ID: ${company_id}`);

    const responseConv = await newCookieUser.request({
      method: Method.POST,
      url: urls.API_JSON2,
      data: {
        ops: [
          {
            title: 'New Process',
            description: '',
            folder_id: 0,
            obj: 'conv',
            conv_type: 'process',
            type: 'create',
            obj_type: 0,
            status: 'active',
            company_id,
          },
        ],
      },
    });
    expect(responseConv.status).toBe(200);
    expect(responseConv.data.ops[0].obj).toBe('conv');
    convIdCompany = responseConv.data.ops[0].obj_id;
    debug(`Created process in company with ID: ${convIdCompany}`);

    const companyTasksCount = 17;
    debug(`Creating ${companyTasksCount} tasks for company process ${convIdCompany}`);

    for (let i = 0; i < companyTasksCount; i++) {
      const responseTask = await newCookieUser.request({
        method: Method.POST,
        url: urls.API_JSON2,
        data: {
          ops: [
            {
              type: 'create',
              obj: 'task',
              conv_id: convIdCompany,
              data: { x: '1' },
              ref: `task_company_${Date.now()}_${i}`,
            },
          ],
        },
      });
      expect(responseTask.status).toBe(200);

      if (i % 5 === 0 && i > 0) {
        debug(`Created ${i} of ${companyTasksCount} tasks for company...`);
      }
    }
    debug(`All ${companyTasksCount} tasks created for company process`);

    const myCorezoidTasksCount = 17;
    debug(`Creating ${myCorezoidTasksCount} tasks for my_corezoid process ${convId}`);

    for (let i = 0; i < myCorezoidTasksCount; i++) {
      const responseTask = await newCookieUser.request({
        method: Method.POST,
        url: urls.API_JSON2,
        data: {
          ops: [
            {
              type: 'create',
              obj: 'task',
              conv_id: convId,
              data: { x: '1' },
              ref: `task_mycorezoid_${Date.now()}_${i}`,
            },
          ],
        },
      });
      expect(responseTask.status).toBe(200);

      if (i % 5 === 0 && i > 0) {
        debug(`Created ${i} of ${myCorezoidTasksCount} tasks for my_corezoid...`);
      }
    }
    debug(`All ${myCorezoidTasksCount} tasks created for my_corezoid process`);

    await new Promise(resolve => setTimeout(resolve, 10000));
    debug('Waiting for tacts to decrease in second test...');

    const checkConditions = (response: MyApiResponse): boolean => {
      if (!response || !response.body || !response.body.tacts) {
        debug('Invalid response format:', response);
        return false;
      }

      userTactsAfter2 = response.body.tacts;
      const tactsDecrease = userTactsAfter - userTactsAfter2;

      debug(`Current tacts: ${userTactsAfter2}, decrease from previous: ${tactsDecrease}`);

      return response.status === 200 && response.body.obj_id === userId && tactsDecrease >= tactsDecreaseMinimum;
    };

    const response = await newCookieSUser.requestWithRetries(
      {
        url: urls.TACTS_INFO,
        method: Method.POST,
        data: {
          obj: 'user',
          obj_id: userId,
        },
      },
      checkConditions,
      { maxRetries: 120, initialDelay: 3000, maxDelay: 3000 },
    );

    expect(response.status).toBe(200);
    expect(response.body.obj_type).toEqual('user');
    debug(
      `Tacts decreased from ${userTactsAfter} to ${userTactsAfter2}, difference: ${userTactsAfter - userTactsAfter2}`,
    );
  });

  afterAll(
    async (): Promise<void> => {
      try {
        if (convId) {
          const responseDel1 = await newCookieUser.request({
            method: Method.POST,
            url: urls.API_JSON2,
            data: { ops: [{ obj: 'conv', obj_id: convId, type: 'delete' }] },
          });
          expect(responseDel1.status).toBe(200);
          debug(`Deleted process ${convId}`);
        }

        if (convIdCompany) {
          const responseDel2 = await newCookieUser.request({
            method: Method.POST,
            url: urls.API_JSON2,
            data: { ops: [{ obj: 'conv', obj_id: convIdCompany, type: 'delete' }] },
          });
          expect(responseDel2.status).toBe(200);
          debug(`Deleted company process ${convIdCompany}`);
        }

        if (company_id) {
          const response = await newCookieSUser.request({
            url: `${urlsSS.WORKSPACE}/${company_id}`,
            method: Method.DELETE,
          });
          expect(response.status).toBe(200);
          debug(`Deleted company ${company_id}`);
        }
      } catch (err) {
        error('Error during cleanup:', err);
      }
    },
  );
});
