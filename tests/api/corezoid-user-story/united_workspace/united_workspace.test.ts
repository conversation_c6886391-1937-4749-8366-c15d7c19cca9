import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../infrastructure/model/User';
import { application } from '../../../../application/Application';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { createAuthUser, Method } from '../../../../utils/request';

describe('United workspace', () => {
  let company_id: any;
  let company_id1: any;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let token: string;
  let tokenAuth: any;
  let host: string;
  let hostSS: string;
  let idW: string | number;
  let idW1: string | number;
  let nameCor: string;
  let nameSS: string;
  let nameSSnew: string;
  let nameCorApi: string;
  let owner_id: string | number;
  let loginInv: string;
  let nameInv: string;
  let login1: string;
  let name1: string;
  let login2: string;
  let name2: string;
  let invId: string | number;
  let user: User;
  let cookieUser: string;
  let cookieUserAuth: any;
  let userID1: string | number;
  let userID2: string | number;
  let conv_id1: string | number;
  let conv_id2: string | number;
  let newApi: ApiKeyClient;
  let newApiKeyId: string | number;
  let url: string;
  let id: string | number;
  let value: string | number;
  let user0New: User;
  let apiUserTokenNew: ApiUserClient;
  let apiUserCookieOwner: ApiUserClient;

  beforeAll(async () => {
    await ConfigurationManager.getConfiguration().initialize();
    const config = ConfigurationManager.getConfiguration();
    host = config.getApiUrl();
    hostSS = config.getSSUrl();
    token = await application.createToken(0);
    tokenAuth = createAuthUser(token, 'token');
    user0New = await application.getAuthorizedUser();
    apiUserTokenNew = await application.getApiUserClient(user0New);

    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);

    nameCor = 'NewCorezoidCompany';
    nameCorApi = 'NewCorezoidCompanyApi';
    nameSS = 'newWorkspaceSS';
    nameSSnew = 'newWorkspaceSSModify';
    loginInv = '<EMAIL>';
    login1 = '<EMAIL>';
    login2 = '<EMAIL>';

    const responseList = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'values',
    });
    expect(responseList.status).toBe(200);
    expect(responseList.body.result).toBe('ok');
    expect(responseList.body.list).toEqual(
      expect.arrayContaining([expect.objectContaining({ key: `capi_user_notify_conv` })]),
    );
    value = (responseList.body.list as Array<any>).find(item => item.key === `capi_user_notify_conv`).value;
    id = (responseList.body.list as Array<any>).find(item => item.key === `capi_user_notify_conv`).id;
  });

  test('should create request create_company Corezoid by simulator_token', async () => {
    const response = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        name: nameCor,
        description: 'company',
        site: 'corezoid.com',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    company_id = response.body.ops[0].obj_id;

    const responseListCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(responseListCor.status).toBe(200);
    expect(responseListCor.body.ops[0].proc).toBe('ok');
    expect((responseListCor.body.ops[0].list as Array<any>).find(item => item.company_id === company_id).title).toEqual(
      nameCor,
    );
    owner_id = (responseListCor.body.ops[0].list as Array<any>).find(item => item.company_id === company_id)
      .owner_user_id;

    await new Promise(r => setTimeout(r, 6000));

    const responseListSS = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces?limit=100&offset=400`,
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).find(item => item.ext_id === company_id).name).toEqual(nameCor);
    idW = (responseListSS.data.data as Array<any>).find(item => item.ext_id === company_id).id;
    expect((responseListSS.data.data as Array<any>).find(item => item.ext_id === company_id).owners[0].id).toEqual(
      owner_id,
    );
  });

  test('should create request delete_company SS by simulator_token', async () => {
    const response = await tokenAuth.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${idW}`,
    });
    expect(response.status).toBe(200);

    const responseListSS = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces?limit=100&offset=300`,
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).filter(item => item.company_id === company_id)).toBeEmpty();

    await new Promise(r => setTimeout(r, 2000));

    const responseListCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(responseListCor.status).toBe(200);
    expect(responseListCor.body.ops[0].proc).toBe('ok');
    expect((responseListCor.body.ops[0].list as Array<any>).filter(item => item.company_id === company_id)).toBeEmpty();
  });

  test('should create request create_company SS by simulator_token', async () => {
    const response = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces`,
      data: { name: nameSS },
    });
    expect(response.status).toBe(201);
    expect(response.data.status).toBe('active');
    expect(response.data.name).toBe(nameSS);
    idW = response.data.id;
    company_id = response.data.ext_id;
    owner_id = response.data.owners[0].id;

    const responseListSS = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces?limit=100&offset=400`,
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).find(item => item.ext_id === company_id).name).toEqual(nameSS);
    idW = (responseListSS.data.data as Array<any>).find(item => item.ext_id === company_id).id;

    await new Promise(r => setTimeout(r, 5000));

    const responseListCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(responseListCor.status).toBe(200);
    expect(responseListCor.body.ops[0].proc).toBe('ok');
    expect((responseListCor.body.ops[0].list as Array<any>).find(item => item.company_id === company_id).title).toEqual(
      nameSS,
    );
    expect(
      (responseListCor.body.ops[0].list as Array<any>).find(item => item.company_id === company_id).owner_user_id,
    ).toEqual(owner_id);
  });

  test('should create request modify_company SS by simulator_token', async () => {
    const response = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${idW}`,
      data: { name: nameSSnew, status: 'active' },
    });
    expect(response.status).toBe(200);
    expect(response.data.name).toBe(nameSSnew);

    const responseListSS = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces?limit=100&offset=400`,
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).find(item => item.ext_id === company_id).name).toEqual(nameSSnew);

    await new Promise(r => setTimeout(r, 4000));

    const responseListCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(responseListCor.status).toBe(200);
    expect(responseListCor.body.ops[0].proc).toBe('ok');
    expect((responseListCor.body.ops[0].list as Array<any>).find(item => item.company_id === company_id).title).toEqual(
      nameSSnew,
    );
    expect(
      (responseListCor.body.ops[0].list as Array<any>).find(item => item.company_id === company_id).owner_user_id,
    ).toEqual(owner_id);
  });

  test('should create request create_invite Corezoid by simulator_token', async () => {
    const response = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        login: loginInv,
        login_type: 'google',
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('invite');

    const responseListSS = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).find(item => item.login === loginInv).status).toEqual('active');
    invId = (responseListSS.data.data as Array<any>).find(item => item.login === loginInv).id;

    const responseDelSS = await tokenAuth.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites/${invId}`,
    });
    expect(responseDelSS.status).toBe(200);

    const responseListSSDel = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSDel.status).toBe(200);
    expect((responseListSSDel.data.data as Array<any>).filter(item => item.login === loginInv)).toBeEmpty();
  });

  test.skip('should create request create_invite Corezoid and confirm invite corezoid', async () => {
    user = await application.getAuthorizedUser({ company: {} }, 0);
    apiUserCookieOwner = await application.getApiUserClient(user);

    const responseInvite = await apiUserCookieOwner.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        login: loginInv,
        login_type: 'google',
        company_id,
      }),
    );
    expect(responseInvite.status).toBe(200);
    expect(responseInvite.body.ops[0].proc).toBe('ok');
    expect(responseInvite.body.ops[0].obj).toBe('invite');
    url = responseInvite.body.ops[0].url;

    await new Promise(r => setTimeout(r, 3000));

    user = await application.getAuthorizedUser({ company: {} }, 3);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    nameInv = responseMe.data.name;

    await new Promise(r => setTimeout(r, 3000));
    const responseConfirm = await cookieUserAuth.request({
      method: Method.GET,
      url: url,
      data: {},
    });
    expect(responseConfirm.status).toBe(200);

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === loginInv)).toBeArray;
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === nameInv).logins[0].login).toEqual(
      loginInv,
    );
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === nameInv).status).toEqual('active');

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === loginInv).title,
    ).toEqual(nameInv);
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === loginInv).status,
    ).toEqual('actived');
  });

  test('should create requests for create user1', async () => {
    const response = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: {
        login: login1,
        role_id: 2,
      },
    });
    expect(response.status).toBe(201);
    expect(response.data.status).toBe('active');
    expect(response.data.login).toBe(login1);

    user = await application.getAuthorizedUser({ company: {} }, 6);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');

    const responseListSS = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).find(item => item.login === login1).status).toEqual('active');

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    name1 = responseMe.data.name;

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === login1)).toBeArray;
    const logins = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).logins;
    expect((logins as Array<any>).find(item => item.type === 7).login).toEqual(login1);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).status).toEqual('active');
    userID1 = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).id;

    await new Promise(r => setTimeout(r, 10000));

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).title,
    ).toEqual(name1);
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).status,
    ).toEqual('actived');

    const apiUserCookie = await application.getApiUserClient(user);

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('ok');
    conv_id1 = responseCreateConv.body.ops[0].obj_id;
  });

  test('should create requests for modify user status in SS', async () => {
    const response = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users/${userID1}`,
      data: { status: 'block' },
    });
    expect(response.status).toBe(200);

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    const logins = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).logins;
    expect((logins as Array<any>).find(item => item.type === 7).login).toEqual(login1);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).status).toEqual('blocked');

    await new Promise(r => setTimeout(r, 2000));

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).status,
    ).toEqual('blocked');

    const responseShowConvBlock = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConvBlock.status).toBe(200);
    expect(responseShowConvBlock.body.ops[0].status).toBe('active');

    const response1 = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users/${userID1}`,
      data: { status: 'active' },
    });
    expect(response1.status).toBe(200);

    const responseListSSUsers1 = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers1.status).toBe(200);
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name1).logins[0].login).toEqual(
      login1,
    );
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name1).status).toEqual('active');

    await new Promise(r => setTimeout(r, 6000));

    const responseListUserCor1 = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor1.status).toBe(200);
    expect(responseListUserCor1.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor1.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).status,
    ).toEqual('actived');

    const responseShowConvActive = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConvActive.status).toBe(200);
    expect(responseShowConvActive.body.ops[0].status).toBe('active');
  });

  test('should create requests for delete user in SS', async () => {
    const response = await tokenAuth.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users/${userID1}`,
      data: {},
    });
    expect(response.status).toBe(200);

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === login1)).toBeEmpty();
    expect((responseListSSUsers.data.data as Array<any>).filter(item => item.name === name1)).toBeEmpty();

    await new Promise(r => setTimeout(r, 2000));

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).filter(item => item.logins[0]?.login === login1),
    ).toBeEmpty();
  });

  test('should create requests for create user2', async () => {
    const response = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        login_type: 'google',
        company_id,
        login: login2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('invite');

    const responseListSS = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).find(item => item.login === login2).status).toEqual('active');

    user = await application.getAuthorizedUser({ company: {} }, 2);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    name2 = responseMe.data.name;

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === login2)).toBeArray;
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name2).logins[0].login).toEqual(
      login2,
    );

    await new Promise(r => setTimeout(r, 2000));

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login2).status,
    ).toEqual('actived');
    userID2 = (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.title === name2).obj_id;

    const apiUserCookie = await application.getApiUserClient(user);

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUse2',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('ok');
    conv_id2 = responseCreateConv.body.ops[0].obj_id;
  });

  test('should create requests for modify user status in Corezoid', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        company_id,
        obj_id: userID2,
        status: 'blocked',
        blocked_reason: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].status).toBe('blocked');

    await new Promise(r => setTimeout(r, 2000));

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name2).logins[0].login).toEqual(
      login2,
    );
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name2).status).toEqual('blocked');

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login2).status,
    ).toEqual('blocked');

    const responseShowConvBlock = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConvBlock.status).toBe(200);
    expect(responseShowConvBlock.body.ops[0].status).toBe('blocked');

    const response1 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        company_id,
        obj_id: userID2,
        status: 'actived',
        blocked_reason: 'test',
      }),
    );
    expect(response1.status).toBe(200);
    expect(response1.body.ops[0].status).toBe('actived');

    const responseListSSUsers1 = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers1.status).toBe(200);
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name2).logins[0].login).toEqual(
      login2,
    );
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name2).status).toEqual('active');

    await new Promise(r => setTimeout(r, 2000));

    const responseListUserCor1 = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor1.status).toBe(200);
    expect(responseListUserCor1.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor1.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login2).status,
    ).toEqual('actived');

    const responseShowConvActive = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConvActive.status).toBe(200);
    expect(responseShowConvActive.body.ops[0].status).toBe('active');
  });

  test('should create requests corezoid for modify user status in instance', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID2,
        status: 'blocked',
        blocked_reason: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].status).toBe('blocked');

    await new Promise(r => setTimeout(r, 2000));

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name2).logins[0].login).toEqual(
      login2,
    );

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    expect(responseMe.data.status).toBe('blocked');

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login2).status,
    ).toEqual('blocked');

    const responseMeCor = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMeCor.status).toBe(200);
    expect(responseMeCor.data.login).toBe(login2);
    expect(responseMeCor.data.status).toBe('blocked');

    const responseShowConvBlock = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConvBlock.status).toBe(200);
    expect(responseShowConvBlock.body.ops[0].status).toBe('blocked');

    const response1 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID2,
        status: 'actived',
        blocked_reason: 'test',
      }),
    );
    expect(response1.status).toBe(200);
    expect(response1.body.ops[0].status).toBe('actived');

    const responseListSSUsers1 = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers1.status).toBe(200);
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name2).logins[0].login).toEqual(
      login2,
    );
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name2).status).toEqual('active');

    const responseMe1 = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe1.status).toBe(200);
    expect(responseMe1.data.status).toBe('active');

    await new Promise(r => setTimeout(r, 2000));

    const responseListUserCor1 = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor1.status).toBe(200);
    expect(responseListUserCor1.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor1.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login2).status,
    ).toEqual('actived');

    const responseMeCor1 = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMeCor1.status).toBe(200);
    expect(responseMeCor1.data.login).toBe(login2);
    expect(responseMeCor1.data.status).toBe('actived');

    const responseShowConvActive = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConvActive.status).toBe(200);
    expect(responseShowConvActive.body.ops[0].status).toBe('active');
  });

  test('should create requests corezoid for modify user status in instance (without process)', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID2,
        status: 'blocked',
        blocked_reason: 'test',
        obj_type: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].status).toBe('blocked');

    await new Promise(r => setTimeout(r, 2000));

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name2).logins[0].login).toEqual(
      login2,
    );

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    expect(responseMe.data.status).toBe('blocked');

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login2).status,
    ).toEqual('blocked');

    const responseMeCor = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMeCor.status).toBe(200);
    expect(responseMeCor.data.login).toBe(login2);
    expect(responseMeCor.data.status).toBe('blocked');

    const responseShowConvBlock = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConvBlock.status).toBe(200);
    expect(responseShowConvBlock.body.ops[0].status).toBe('active');

    const response1 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID2,
        status: 'actived',
        blocked_reason: 'test',
        obj_type: 'user',
      }),
    );
    expect(response1.status).toBe(200);
    expect(response1.body.ops[0].status).toBe('actived');

    const responseListSSUsers1 = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers1.status).toBe(200);
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name2).logins[0].login).toEqual(
      login2,
    );
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name2).status).toEqual('active');

    const responseMe1 = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe1.status).toBe(200);
    expect(responseMe1.data.status).toBe('active');

    await new Promise(r => setTimeout(r, 2000));

    const responseListUserCor1 = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor1.status).toBe(200);
    expect(responseListUserCor1.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor1.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login2).status,
    ).toEqual('actived');

    const responseMeCor1 = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMeCor1.status).toBe(200);
    expect(responseMeCor1.data.login).toBe(login2);
    expect(responseMeCor1.data.status).toBe('actived');

    const responseShowConvActive = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConvActive.status).toBe(200);
    expect(responseShowConvActive.body.ops[0].status).toBe('active');
  });

  test('should create requests corezoid for modify status in instance (only process)', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID2,
        status: 'blocked',
        blocked_reason: 'test',
        obj_type: 'process',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].status).toBe('blocked');

    await new Promise(r => setTimeout(r, 2000));

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name2).logins[0].login).toEqual(
      login2,
    );

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    expect(responseMe.data.status).toBe('active');

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login2).status,
    ).toEqual('actived');

    const responseMeCor = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMeCor.status).toBe(200);
    expect(responseMeCor.data.login).toBe(login2);
    expect(responseMeCor.data.status).toBe('actived');

    const responseShowConvBlock = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConvBlock.status).toBe(200);
    expect(responseShowConvBlock.body.ops[0].status).toBe('blocked');

    const response1 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID2,
        status: 'actived',
        blocked_reason: 'test',
        obj_type: 'process',
      }),
    );
    expect(response1.status).toBe(200);
    expect(response1.body.ops[0].status).toBe('actived');

    const responseListSSUsers1 = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers1.status).toBe(200);
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name2).logins[0].login).toEqual(
      login2,
    );
    expect((responseListSSUsers1.data.data as Array<any>).find(item => item.name === name2).status).toEqual('active');

    const responseMe1 = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe1.status).toBe(200);
    expect(responseMe1.data.status).toBe('active');

    await new Promise(r => setTimeout(r, 2000));

    const responseListUserCor1 = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor1.status).toBe(200);
    expect(responseListUserCor1.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor1.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login2).status,
    ).toEqual('actived');

    const responseMeCor1 = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMeCor1.status).toBe(200);
    expect(responseMeCor1.data.login).toBe(login2);
    expect(responseMeCor1.data.status).toBe('actived');

    const responseShowConvActive = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConvActive.status).toBe(200);
    expect(responseShowConvActive.body.ops[0].status).toBe('active');
  });

  test('should create requests corezoid for create company by api_key/ delete in ss', async () => {
    user = await application.getAuthorizedUser({ company: {} }, 2);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');

    const apiUserCookie = await application.getApiUserClient(user);

    const CreateKeyResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'apiUser',
        logins: [{ type: 'api' }],
        company_id,
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);

    const user_api = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
      secret: user_api.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user_api.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    newApiKeyId = +newApiKey.id;

    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        name: nameCorApi,
        description: 'company',
        site: 'corezoid.com',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    company_id1 = response.body.ops[0].obj_id;

    const responseList = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.company_id === company_id1).title).toEqual(
      nameCorApi,
    );
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.company_id === company_id1).owner_user_id,
    ).toEqual(newApiKeyId);

    await new Promise(r => setTimeout(r, 8000));

    const responseListSS = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces`,
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).find(item => item.ext_id === company_id1).name).toEqual(nameCorApi);
    idW1 = (responseListSS.data.data as Array<any>).find(item => item.ext_id === company_id1).id;
    expect((responseListSS.data.data as Array<any>).find(item => item.ext_id === company_id1).owners[0].name).toEqual(
      name2,
    );

    const responseDel = await cookieUserAuth.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${idW1}`,
    });
    expect(responseDel.status).toBe(200);

    await new Promise(r => setTimeout(r, 2000));

    const responseListSSD = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces`,
    });
    expect(responseListSSD.status).toBe(200);
    expect((responseListSSD.data.data as Array<any>).filter(item => item.company_id === company_id1)).toBeEmpty();

    const responseListDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(responseListDel.status).toBe(200);
    expect(responseListDel.body.ops[0].proc).toBe('ok');
    expect(
      (responseListDel.body.ops[0].list as Array<any>).filter(item => item.company_id === company_id1),
    ).toBeEmpty();
  });

  test('should create requests for delete user in Corezoid', async () => {
    const response = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: userID2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 20000));

    const responseListUserCor = await apiUserTokenNew.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).filter(item => item.logins[0]?.login === login2),
    ).toBeEmpty();

    await new Promise(r => setTimeout(r, 20000));
    await new Promise(r => setTimeout(r, 20000));

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === login2)).toBeEmpty();
    expect((responseListSSUsers.data.data as Array<any>).filter(item => item.name === name2)).toBeEmpty();
  });

  afterAll(async () => {
    const response = await tokenAuth.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${idW}`,
    });
    expect(response.status).toBe(200);

    const responseSetValue = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      id,
      key: 'capi_user_notify_conv',
      value,
      obj_type: 'integer',
      is_required: false,
      allowed: [],
    });
    expect(responseSetValue.status).toBe(200);
    expect(responseSetValue.body.result).toBe('ok');
  });
});
