import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../infrastructure/model/User';
import { application } from '../../../../application/Application';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { createAuthUser, Method } from '../../../../utils/request';
import { debug } from '../../../../support/utils/logger';
import { MyApiResponse, makeRequestWithRetries } from '../../../../utils/requestRetries';

describe('United workspace', () => {
  let company_id: any;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let token: string;
  let hostSS: string;
  let idW: string | number;
  let nameSS: string;
  let login1: string;
  let name1: string;
  let login2: string;
  let login3: string;
  let name3: string;
  let roleId: string | number;
  let user: User;
  let cookieUser: string;
  let cookieUserAuth: any;
  let tokenAuth: any;
  let userID1: number;
  let userID3: number;
  let id: string | number;
  let value: string | number;
  let groupSS: string | number;
  let groupCor: string | number;
  let owner_request_id: string | number;
  let user_idCor: number;
  let loginOwner: string;

  let user0: User;
  let apiUserToken: ApiUserClient;
  let apiUserCookie: ApiUserClient;

  beforeAll(async () => {
    await ConfigurationManager.getConfiguration().initialize();
    const config = ConfigurationManager.getConfiguration();
    hostSS = config.getSSUrl();
    token = await application.createToken(0);
    tokenAuth = createAuthUser(token, 'token');
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    nameSS = 'newWorkspaceSS';
    login1 = '<EMAIL>';
    login3 = '<EMAIL>';
    login2 = '<EMAIL>';
    loginOwner = '<EMAIL>';

    user0 = await application.getAuthorizedUser();
    apiUserToken = await application.getApiUserClient(user0);

    const responseList = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'values',
    });
    expect(responseList.status).toBe(200);
    expect(responseList.body.result).toBe('ok');
    expect(responseList.body.list).toEqual(
      expect.arrayContaining([expect.objectContaining({ key: `capi_user_notify_conv` })]),
    );
    value = (responseList.body.list as Array<any>).find(item => item.key === `capi_user_notify_conv`).value;
    id = (responseList.body.list as Array<any>).find(item => item.key === `capi_user_notify_conv`).id;

    const response = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces`,
      data: { name: nameSS },
    });
    expect(response.status).toBe(201);
    expect(response.data.status).toBe('active');
    expect(response.data.name).toBe(nameSS);
    idW = response.data.id;
    company_id = response.data.ext_id;
  });

  test('should create requests for create user1', async () => {
    const response = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: {
        login: login1,
        role_id: 2,
      },
    });
    expect(response.status).toBe(201);
    expect(response.data.status).toBe('active');
    expect(response.data.login).toBe(login1);

    user = await application.getAuthorizedUser({ company: {} }, 6);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');
    apiUserCookie = await application.getApiUserClient(user);

    const responseListSS = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).find(item => item.login === login1).status).toEqual('active');

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    name1 = responseMe.data.name;

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === login1)).toBeArray;
    const logins = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).logins;
    expect((logins as Array<any>).find(item => item.type === 7).login).toEqual(login1);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).status).toEqual('active');
    userID1 = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).id;

    await new Promise(r => setTimeout(r, 10000));

    const responseListUserCor = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).title,
    ).toEqual(name1);
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).status,
    ).toEqual('actived');

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('ok');
  });

  test('should modify role to Guest for user1', async () => {
    const response = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users/${userID1}/roles`,
      data: [
        { id: 2, active: false },
        { id: 4, active: true },
      ],
    });
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 20000));

    user = await application.getAuthorizedUser({ company: {} }, 6);
    cookieUser = user.cookieUser;
    apiUserCookie = await application.getApiUserClient(user);

    await new Promise(r => setTimeout(r, 10000));

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === login1)).toBeArray;
    const logins = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).logins;
    expect((logins as Array<any>).find(item => item.type === 7).login).toEqual(login1);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).status).toEqual('active');
    userID1 = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).id;

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    name1 = responseMe.data.name;

    await new Promise(r => setTimeout(r, 20000));

    const responseListUserCor = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect((responseListUserCor.body.ops[0].list as Array<any>).some(item => item.logins[0]?.login === login1)).toBe(
      true,
    );

    await new Promise(r => setTimeout(r, 20000));

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('error');
    expect(responseCreateConv.body.ops[0].description).toBe('You do not have permission to perform this action');

    const responseCreateProject = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        title: 'test-project',
        short_name: 'test-project',
        company_id,
        stages: [
          {
            title: 'prod',
          },
        ],
      }),
    );
    expect(responseCreateProject.status).toBe(200);
    expect(responseCreateProject.body.ops[0].proc).toBe('error');
    expect(responseCreateProject.body.ops[0].description).toBe('You do not have permission to perform this action');

    await new Promise(r => setTimeout(r, 20000));
  });

  test('should modify role guest to Admin for user1', async () => {
    const response = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users/${userID1}/roles`,
      data: [
        { id: 2, active: true },
        { id: 4, active: false },
      ],
    });
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 20000));

    user = await application.getAuthorizedUser({ company: {} }, 6);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');
    apiUserCookie = await application.getApiUserClient(user);

    await new Promise(r => setTimeout(r, 20000));

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === login1)).toBeArray;
    const logins = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).logins;
    expect((logins as Array<any>).find(item => item.type === 7).login).toEqual(login1);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).status).toEqual('active');
    userID1 = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).id;

    await new Promise(r => setTimeout(r, 20000));

    const responseListUserCor = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).title,
    ).toEqual(name1);
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).status,
    ).toEqual('actived');

    await new Promise(r => setTimeout(r, 10000));

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('ok');
  });

  test('should create requests for create user3 Guest', async () => {
    const response = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: {
        login: login3,
        role_id: 4,
      },
    });
    expect(response.status).toBe(201);
    expect(response.data.status).toBe('active');
    expect(response.data.login).toBe(login3);

    user = await application.getAuthorizedUser({ company: {} }, 4);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');
    apiUserCookie = await application.getApiUserClient(user);

    const responseListSS = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSS.status).toBe(200);
    expect((responseListSS.data.data as Array<any>).find(item => item.login === login3).status).toEqual('active');

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    name3 = responseMe.data.name;

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === login3)).toBeArray;
    const logins = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name3).logins;
    expect((logins as Array<any>).find(item => item.type === 7).login).toEqual(login3);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name3).status).toEqual('active');
    userID3 = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name3).id;

    await new Promise(r => setTimeout(r, 10000));

    const responseListUserCor = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).title,
    ).toEqual(name1);
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login1).status,
    ).toEqual('actived');
    expect((responseListUserCor.body.ops[0].list as Array<any>).some(item => item.logins[0]?.login === login3)).toBe(
      true,
    );

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('error');
    expect(responseCreateConv.body.ops[0].description).toBe('You do not have permission to perform this action');

    const responseCreateProject = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        title: 'test-project1',
        short_name: 'test-project1',
        company_id,
        stages: [
          {
            title: 'prod',
          },
        ],
      }),
    );
    expect(responseCreateProject.status).toBe(200);
    expect(responseCreateProject.body.ops[0].proc).toBe('error');
    expect(responseCreateProject.body.ops[0].description).toBe('You do not have permission to perform this action');

    await new Promise(r => setTimeout(r, 5000));
  });

  test('should add new role Create_projects for user1', async () => {
    const responseRole = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces/${company_id}/roles`,
      data: {
        name: 'create_project',
        perms: [{ id: 'workspace:create_projects', type: 'perms.corezoid' }],
        users: [userID3],
      },
    });
    expect(responseRole.status).toBe(201);
    roleId = responseRole.data.id;

    await new Promise(r => setTimeout(r, 10000));

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('error');
    expect(responseCreateConv.body.ops[0].description).toBe('You do not have permission to perform this action');

    const responseCreateProject = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        title: 'test-project',
        short_name: 'test-project',
        company_id,
        stages: [
          {
            title: 'prod',
          },
        ],
      }),
    );
    expect(responseCreateProject.status).toBe(200);
    expect(responseCreateProject.body.ops[0].proc).toBe('ok');
    expect(responseCreateProject.body.ops[0].obj).toBe('project');
  });

  test('should delete perm for role create_projects for user1', async () => {
    const responsePerms = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/roles/perms`,
      data: [{ role_id: roleId, active: false, id: 'workspace:create_projects', type: 'perms.corezoid' }],
    });
    expect(responsePerms.status).toBe(200);

    await new Promise(r => setTimeout(r, 10000));

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('error');
    expect(responseCreateConv.body.ops[0].description).toBe('You do not have permission to perform this action');

    const responseCreateProject = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        title: 'test-project',
        short_name: 'test-project',
        company_id,
        stages: [
          {
            title: 'prod',
          },
        ],
      }),
    );
    expect(responseCreateProject.status).toBe(200);
    expect(responseCreateProject.body.ops[0].proc).toBe('error');
    expect(responseCreateProject.body.ops[0].description).toBe('You do not have permission to perform this action');
  });

  test('should delete perm for role create_projects for user1', async () => {
    const responsePerms = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/roles/perms`,
      data: [{ role_id: roleId, active: true, id: 'workspace:create_standalone_entities', type: 'perms.corezoid' }],
    });
    expect(responsePerms.status).toBe(200);

    await new Promise(r => setTimeout(r, 10000));

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('ok');

    const responseCreateProject = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        title: 'test-project',
        short_name: 'test-project',
        company_id,
        stages: [
          {
            title: 'prod',
          },
        ],
      }),
    );
    expect(responseCreateProject.status).toBe(200);
    expect(responseCreateProject.body.ops[0].proc).toBe('error');
    expect(responseCreateProject.body.ops[0].description).toBe('You do not have permission to perform this action');

    const responsePerms1 = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/roles/perms`,
      data: [{ role_id: roleId, active: false, id: 'workspace:create_standalone_entities', type: 'perms.corezoid' }],
    });
    expect(responsePerms1.status).toBe(200);

    await new Promise(r => setTimeout(r, 10000));

    const responseCreateConv1 = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv1.status).toBe(200);
    expect(responseCreateConv1.body.ops[0].proc).toBe('error');
    expect(responseCreateConv1.body.ops[0].description).toBe('You do not have permission to perform this action');
  });

  test('should add role to Guest + Admin for user1', async () => {
    const response = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users/${userID3}/roles`,
      data: [{ id: 2, active: true }],
    });
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 10000));

    user = await application.getAuthorizedUser({ company: {} }, 1);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');
    apiUserCookie = await application.getApiUserClient(user);

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data[0].logins as Array<any>).filter(item => item.login === login3)).toBeArray;
    const logins = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name3).logins;
    expect((logins as Array<any>).find(item => item.type === 7).login).toEqual(login3);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name3).status).toEqual('active');

    await new Promise(r => setTimeout(r, 15000));

    const responseListUserCor = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(responseListUserCor.status).toBe(200);
    expect(responseListUserCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login3).title,
    ).toEqual(name3);
    expect(
      (responseListUserCor.body.ops[0].list as Array<any>).find(item => item.logins[0]?.login === login3).status,
    ).toEqual('actived');

    const responseCreateConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.body.ops[0].proc).toBe('ok');

    const responseCreateProject = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        title: 'test-project2',
        short_name: 'test-project2',
        company_id,
        stages: [
          {
            title: 'prod',
          },
        ],
      }),
    );
    expect(responseCreateProject.status).toBe(200);
    expect(responseCreateProject.body.ops[0].proc).toBe('ok');
    expect(responseCreateProject.body.ops[0].obj).toBe('project');
  });

  test('should create group SS and Corezoid', async () => {
    const response = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups`,
      data: { name: 'groupSS', users: [] },
    });
    expect(response.status).toBe(201);
    groupSS = response.data.id;

    const responseCreateCorezoid = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        title: 'groupCor',
        company_id,
        obj_type: 'admins',
      }),
    );
    expect(responseCreateCorezoid.status).toBe(200);
    expect(responseCreateCorezoid.body.ops[0].proc).toBe('ok');
    groupCor = responseCreateCorezoid.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 10000));

    const responseListSSGroups = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSGroups.status).toBe(200);
    expect((responseListSSGroups.data as Array<any>).find(item => item.name === 'groupSS').user_count).toEqual(0);
    expect((responseListSSGroups.data as Array<any>).find(item => item.name === 'groupCor').user_count).toEqual(0);

    const responseListGroupCor = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'group',
        company_id,
      }),
    );
    expect(responseListGroupCor.status).toBe(200);
    expect(responseListGroupCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListGroupCor.body.ops[0].list as Array<any>).find(item => item.title === 'groupCor').is_owner,
    ).toEqual(true);
    expect(
      (responseListGroupCor.body.ops[0].list as Array<any>).find(item => item.title === 'groupSS').is_owner,
    ).toEqual(true);
  });

  test('should modify group SS and Corezoid', async () => {
    const response = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups/${groupSS}`,
      data: { name: 'groupSSModify' },
    });
    expect(response.status).toBe(200);

    const responseCreateCorezoid = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        title: 'groupCorModify',
        obj_id: groupCor,
        company_id,
        obj_type: 'admins',
        status: 'actived',
      }),
    );
    expect(responseCreateCorezoid.status).toBe(200);
    expect(responseCreateCorezoid.body.ops[0].proc).toBe('ok');

    await new Promise(r => setTimeout(r, 10000));

    const responseListSSGroups = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSGroups.status).toBe(200);
    expect((responseListSSGroups.data as Array<any>).find(item => item.name === 'groupSSModify').user_count).toEqual(0);
    expect((responseListSSGroups.data as Array<any>).find(item => item.name === 'groupCorModify').user_count).toEqual(
      0,
    );

    const responseListGroupCor = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'group',
        company_id,
      }),
    );
    expect(responseListGroupCor.status).toBe(200);
    expect(responseListGroupCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListGroupCor.body.ops[0].list as Array<any>).find(item => item.title === 'groupCorModify').is_owner,
    ).toEqual(true);
    expect(
      (responseListGroupCor.body.ops[0].list as Array<any>).find(item => item.title === 'groupSSModify').is_owner,
    ).toEqual(true);
  });

  test('should add user to group SS and Corezoid', async () => {
    const response = await tokenAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups/${groupSS}/users`,
      data: [{ id: userID1, active: true }],
    });
    expect(response.status).toBe(200);

    const responseCreateCorezoid = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        group_id: groupCor,
        obj_id: userID1,
        company_id,
        level: 1,
      }),
    );
    expect(responseCreateCorezoid.status).toBe(200);
    expect(responseCreateCorezoid.body.ops[0].proc).toBe('ok');

    await new Promise(r => setTimeout(r, 10000));

    const responseListSSGroups = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSGroups.status).toBe(200);
    expect((responseListSSGroups.data as Array<any>).find(item => item.name === 'groupSSModify').user_count).toEqual(1);
    expect((responseListSSGroups.data as Array<any>).find(item => item.name === 'groupCorModify').user_count).toEqual(
      1,
    );

    const responseListSSGroupsUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups/${groupSS}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSGroupsUsers.status).toBe(200);
    expect((responseListSSGroupsUsers.data[0].logins as Array<any>).find(item => item.login === login1).type).toEqual(
      2,
    );

    const responseListGroupCor = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        list_obj: 'user',
        company_id,
        obj_id: groupCor,
      }),
    );
    expect(responseListGroupCor.status).toBe(200);
    expect(responseListGroupCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListGroupCor.body.ops[0].list[0].logins as Array<any>).find(item => item.login === login1).type,
    ).toEqual('google');
  });

  test('should delete group SS and Corezoid', async () => {
    const response = await tokenAuth.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups/${groupSS}`,
      data: {},
    });
    expect(response.status).toBe(200);

    const responseCreateCorezoid = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupCor,
        company_id,
      }),
    );
    expect(responseCreateCorezoid.status).toBe(200);
    expect(responseCreateCorezoid.body.ops[0].proc).toBe('ok');

    await new Promise(r => setTimeout(r, 10000));

    const responseListSSGroups = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSGroups.status).toBe(200);
    expect(responseListSSGroups.data).toBeArrayOfSize(0);

    const responseListGroupCor = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'group',
        company_id,
      }),
    );
    expect(responseListGroupCor.status).toBe(200);
    expect(responseListGroupCor.body.ops[0].proc).toBe('ok');
    expect(responseListGroupCor.body.ops[0].list).toBeArrayOfSize(0);
  });

  test('should modify owner group SS and Corezoid', async () => {
    const responseCreateCor = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups`,
      data: { name: 'groupSS', users: [] },
    });
    expect(responseCreateCor.status).toBe(201);
    groupSS = responseCreateCor.data.id;

    const responseCreate = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        title: 'groupCor',
        company_id,
        obj_type: 'admins',
      }),
    );
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.body.ops[0].proc).toBe('ok');
    groupCor = responseCreate.body.ops[0].obj_id;

    const response = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups/${groupSS}/owner`,
      data: { leave: false, owner_id: userID1 },
    });
    expect(response.status).toBe(200);

    const responseCreateCorezoid = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_type: 'group',
        save_src_privs: true,
        obj_id: groupCor,
        company_id,
        obj_to_id: userID1,
      }),
    );
    expect(responseCreateCorezoid.status).toBe(200);
    expect(responseCreateCorezoid.body.ops[0].proc).toBe('ok');

    await new Promise(r => setTimeout(r, 20000));

    const responseListSSGroups = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/groups?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSGroups.status).toBe(200);
    expect((responseListSSGroups.data as Array<any>).find(item => item.name === 'groupSS').owners[0].id).toEqual(
      userID1,
    );
    expect((responseListSSGroups.data as Array<any>).find(item => item.name === 'groupCor').owners[0].id).toEqual(
      userID1,
    );

    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.title === 'groupSS' && item.is_owner === false)
      );
    }

    await new Promise(r => setTimeout(r, 5000));

    const responseListGroupCor = await makeRequestWithRetries(
      async () => {
        return await apiUserToken.requestWithToken(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.COMPANY_USERS,
            filter: 'shared',
            company_id,
          }),
        );
      },
      checkConditions,
      100,
    );

    expect(responseListGroupCor.status).toBe(200);
    debug(JSON.stringify(responseListGroupCor.body));
    expect(responseListGroupCor.body.ops[0].proc).toBe('ok');
    expect(
      (responseListGroupCor.body.ops[0].list as Array<any>).find(item => item.title === 'groupCor').is_owner,
    ).toEqual(false);
    expect(
      (responseListGroupCor.body.ops[0].list as Array<any>).find(item => item.title === 'groupSS').is_owner,
    ).toEqual(false);
    expect(
      (responseListGroupCor.body.ops[0].list as Array<any>).find(item => item.title === 'groupCor').owner_id,
    ).toEqual(userID1);
    expect(
      (responseListGroupCor.body.ops[0].list as Array<any>).find(item => item.title === 'groupSS').owner_id,
    ).toEqual(userID1);
  });

  test('should modify owner workspace in SS', async () => {
    const responseInvite = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/workspaces/${company_id}/invites`,
      data: {
        login: login2,
        role_id: 2,
      },
    });
    expect(responseInvite.status).toBe(201);
    expect(responseInvite.data.status).toBe('active');
    expect(responseInvite.data.login).toBe(login2);

    user = await application.getAuthorizedUser({ company: {} }, 3);
    cookieUser = user.cookieUser;
    user_idCor = user.id;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');
    apiUserCookie = await application.getApiUserClient(user);

    const responseMe = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMe.status).toBe(200);
    name1 = responseMe.data.name;

    const responseListSSUsers = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}/users?limit=20&offset=0`,
      data: { limit: 20, offset: 0 },
    });
    expect(responseListSSUsers.status).toBe(200);
    expect((responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).status).toEqual('active');
    userID1 = (responseListSSUsers.data.data as Array<any>).find(item => item.name === name1).id;

    const response = await tokenAuth.request({
      method: Method.POST,
      url: `${hostSS}face/api/1/owner_request`,
      data: { workspace_id: `${company_id}`, new_owner_id: userID1, obj_owner_ids: [userID1] },
    });
    expect(response.status).toBe(200);

    const responseGet = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/owner_request`,
    });
    expect(responseGet.status).toBe(200);
    owner_request_id = responseGet.data[0].id;

    const responseApproved = await cookieUserAuth.request({
      method: Method.PUT,
      url: `${hostSS}face/api/1/owner_request/${owner_request_id}`,
      data: { status: 'approved' },
    });
    expect(responseApproved.status).toBe(200);

    await new Promise(r => setTimeout(r, 3000));

    const responseGetWorkspace = await tokenAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}`,
    });
    expect(responseGetWorkspace.status).toBe(404);
    expect(responseGetWorkspace.data.msg).toBe(
      `The specified workspace either does not exist or you don't have access to it. Please, check your link`,
    );

    const responseGetWorkspaceNewOwner = await cookieUserAuth.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/workspaces/${company_id}`,
    });
    expect(responseGetWorkspaceNewOwner.status).toBe(200);
    expect(responseGetWorkspaceNewOwner.data.owners[0].id).toBe(userID1);

    await new Promise(r => setTimeout(r, 5000));

    const responseListCorNewOwner = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(responseListCorNewOwner.status).toBe(200);
    expect(responseListCorNewOwner.body.ops[0].proc).toBe('ok');
    expect(
      (responseListCorNewOwner.body.ops[0].list as Array<any>).find(item => item.company_id === company_id).is_owner,
    ).toEqual(true);
    expect(
      (responseListCorNewOwner.body.ops[0].list as Array<any>).find(item => item.company_id === company_id)
        .owner_user_id,
    ).toEqual(user_idCor);

    function checkConditions(responseListUser: MyApiResponse): boolean {
      return (
        responseListUser.status === 200 &&
        !responseListUser.body.ops[0].list.some((item: any) => item.logins?.[0]?.login === loginOwner)
      );
    }

    const responseListUser = await makeRequestWithRetries(
      async () => {
        return await apiUserCookie.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.COMPANY_USERS,
            company_id,
            keys: { filter: 'user' },
          }),
        );
      },
      checkConditions,
      100,
    );
    expect(responseListUser.status).toBe(200);
    expect(responseListUser.body.ops[0].proc).toBe('ok');
  });

  afterAll(async () => {
    const response = await cookieUserAuth.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${idW}`,
    });
    expect(response.status).toBe(200);

    const responseSetValue = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      id,
      key: 'capi_user_notify_conv',
      value,
      obj_type: 'integer',
      is_required: false,
      allowed: [],
    });
    expect(responseSetValue.status).toBe(200);
    expect(responseSetValue.body.result).toBe('ok');
  });
});
