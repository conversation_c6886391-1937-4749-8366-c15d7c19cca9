import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../infrastructure/model/User';
import { application } from '../../../../application/Application';
import { createAuthUser, Method } from '../../../../utils/request';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';

describe('single_account callback', () => {
  let ssData: any;
  let ssToken: any;
  let workspace_id: string;
  let user: User;
  let cookieUser: any;
  let user2: User;
  let cookieUser2: any;
  let iso: string;
  let ts: number;
  let group_id: number;
  let invite_id: number;

  const TEST_USER_ID = 448384;
  const TEST_API_KEY_ID = 448763;
  const TEST_AUTHOR_ID = 12384;
  const TEST_EMAIL = '<EMAIL>';
  const TEST_API_KEY_NAME = 'testcallbackKey';
  const TEST_GROUP_NAME = 'autoTestCallBack';
  const TEST_GROUP_RENAME = 'autoTestCallBackRename';

  const PERMISSIONS = {
    SA: {
      ids: ['apiManaging', 'groupManaging', 'userManaging', 'viewUsers', 'workspace'],
      type: 'sa',
    },
    CONTROL_FULL: {
      ids: [
        'accounts:management',
        'accounts:readonly',
        'actors:management',
        'communications:readonly',
        'forms:management',
        'forms:readonly',
        'scripts:management',
        'scripts:readonly',
        'transactions:readonly',
      ],
      type: 'control',
    },
    CONTROL_READONLY: {
      ids: ['accounts:readonly'],
      type: 'control',
    },
    COREZOID: {
      ids: ['workspace:create_projects', 'workspace:create_standalone_entities'],
      type: 'corezoid',
    },
    SA_WORKSPACE: {
      ids: ['workspace'],
      type: 'sa',
    },
  };

  const API_SCOPES = {
    CONTROL: {
      ids: [
        'workspaces.readonly',
        'workspaces.management',
        'users.readonly',
        'users.management',
        'transfers.readonly',
        'transfers.management',
        'system_actors.management',
        'forms.readonly',
        'forms.management',
        'attachments.readonly',
        'attachments.management',
        'actors.set_readonly_flag',
        'actors.readonly',
        'actors.management',
      ],
      type: 'control',
    },
    COREZOID: {
      ids: ['second_id', 'first_id'],
      type: 'corezoid',
    },
    ACCOUNT: {
      ids: [
        'workspaces.readonly',
        'users.readonly',
        'users.management',
        'sso.auth',
        'invites.readonly',
        'invites.management',
        'groups.readonly',
      ],
      type: 'account',
    },
  };

  const createTestUser = (): any => ({
    active: true,
    author_id: TEST_AUTHOR_ID,
    id: TEST_USER_ID,
    obj_owner_ids: [],
    perms: [PERMISSIONS.SA, PERMISSIONS.CONTROL_FULL, PERMISSIONS.COREZOID],
    photo: '',
    status: 'added',
  });

  const createSSRequest = (name: string, data: any): any => ({
    id: ts,
    workspace_id,
    created_at: iso,
    name,
    data,
  });

  const performSSRequest = async (name: string, data: any): Promise<any> => {
    const response = await ssToken.request({
      url: ssData.url,
      method: Method.POST,
      data: [createSSRequest(name, data)],
    });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.proc).toBe(PROC_STATUS.OK);
    return response;
  };

  const performInviteAction = async (action: string, extraData = {}): Promise<any> => {
    return performSSRequest(`invite/${action}`, {
      id: invite_id,
      login: TEST_EMAIL,
      ...extraData,
    });
  };

  const performGroupAction = async (action: string, groupName: string, meta: any): Promise<any> => {
    return performSSRequest(`workspace/group/${action}`, {
      id: group_id,
      meta,
      name: groupName,
      owner_ids: [TEST_AUTHOR_ID],
      users: [],
    });
  };

  const getGroupsList = async (): Promise<any> => {
    const response = await cookieUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id: workspace_id,
        filter: 'group',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.request_proc).toBe(PROC_STATUS.OK);
    expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
    return response;
  };

  beforeAll(async () => {
    iso = new Date().toISOString();
    ts = Math.floor(Date.now() / 100000 + 10000000);
    group_id = Math.floor(Math.random() * (200000 - 100000)) + 100001;
    invite_id = Math.floor(Math.random() * (90000 - 80000)) + 80001;

    ssData = ConfigurationManager.getConfiguration().ssCallback();
    ssToken = createAuthUser(ssData.token, 'token');

    user = await application.getAuthorizedUser();
    workspace_id = user.companies[0].id;
    cookieUser = await application.getApiUserClient(user);

    user2 = await application.getAuthorizedUser({}, 7);
    cookieUser2 = await application.getApiUserClient(user2);
  });

  test('should add/del invite', async () => {
    await performInviteAction('add');
    await performInviteAction('del');

    const getUsers = await cookieUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id: workspace_id,
        filter: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(getUsers.status).toBe(RESP_STATUS.OK);
    expect(getUsers.body.request_proc).toBe(PROC_STATUS.OK);
    expect(getUsers.body.ops[0].proc).toBe(PROC_STATUS.OK);
    expect((getUsers.body.ops[0].list as Array<any>).find(item => item.logins[0].login === TEST_EMAIL)).toBeUndefined();
    expect(
      (getUsers.body.ops[0].list as Array<any>).some((user: any) =>
        (user.logins as Array<any>).some((login: any) => login.login === TEST_EMAIL),
      ),
    ).toBe(false);
  });

  test('should add/confirm invite', async () => {
    await performInviteAction('add');

    const inviteConfirm = await ssToken.request({
      url: ssData.url,
      method: Method.POST,
      data: [
        createSSRequest('workspace/user/set', { users: [createTestUser()] }),
        createSSRequest('invite/confirm', {
          id: invite_id,
          login: TEST_EMAIL,
          user_id: TEST_AUTHOR_ID,
        }),
      ],
    });
    expect(inviteConfirm.status).toBe(RESP_STATUS.OK);
    expect(inviteConfirm.data.proc).toBe(PROC_STATUS.OK);

    const getListUsers = await cookieUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id: workspace_id,
        filter: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(getListUsers.status).toBe(RESP_STATUS.OK);
    expect(getListUsers.body.request_proc).toBe(PROC_STATUS.OK);
    expect(getListUsers.body.ops[0].proc).toBe(PROC_STATUS.OK);
    const [list] = getListUsers.body.ops[0].list;
    expect(list.status).toBe('actived');
    expect(list.logins[0].login).toBe(TEST_EMAIL);
  });

  test('should create/modify/delete group', async () => {
    await performGroupAction('add', TEST_GROUP_NAME, {});

    const getListAfterCreate = await getGroupsList();
    expect(getListAfterCreate.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: TEST_GROUP_NAME })]),
    );

    await performGroupAction('set', TEST_GROUP_RENAME, {});

    const getListAfterSet = await getGroupsList();
    expect(getListAfterSet.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: TEST_GROUP_RENAME })]),
    );
    expect(getListAfterSet.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ title: TEST_GROUP_NAME })]),
    );

    await performGroupAction('del', '', { corezoid_owner_id: TEST_AUTHOR_ID });

    const getListAfterDel = await getGroupsList();
    expect(getListAfterDel.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ title: TEST_GROUP_NAME })]),
    );
    expect(getListAfterDel.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ title: TEST_GROUP_RENAME })]),
    );
  });

  test('should api_user/user set and add user in group in workspace', async () => {
    await performSSRequest('workspace/api_user/set', {
      id: TEST_API_KEY_ID,
      name: TEST_API_KEY_NAME,
      scopes: [API_SCOPES.CONTROL, API_SCOPES.COREZOID, API_SCOPES.ACCOUNT],
      url: '',
    });

    await performSSRequest('workspace/user/set', { users: [createTestUser()] });

    await performGroupAction('add', TEST_GROUP_NAME, {});
    const getListAfterCreate = await getGroupsList();
    const newGroupId = getListAfterCreate.body.ops[0].list[0].obj_id;

    await performSSRequest('workspace/group/user/set', {
      id: group_id,
      users: [{ active: true, id: TEST_USER_ID, type: 'user' }],
    });

    const listUsersInGroup = await cookieUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        company_id: workspace_id,
        obj_id: newGroupId,
        list_obj: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(listUsersInGroup.status).toBe(RESP_STATUS.OK);
    expect(listUsersInGroup.body.request_proc).toBe(PROC_STATUS.OK);
    const [list] = listUsersInGroup.body.ops[0].list;
    expect(list.obj).toBe('user');
    expect(list.logins[0].login).toBe(TEST_EMAIL);

    await performSSRequest('workspace/sync', {
      color: 'https://color.com',
      name: 'My group',
      owner_ids: [TEST_AUTHOR_ID],
      photo: 'https://photo.com',
    });
  });

  test('should changed user permissions', async () => {
    await performSSRequest('workspace/user/perm/set', {
      id: TEST_USER_ID,
      perms: [PERMISSIONS.CONTROL_FULL, PERMISSIONS.COREZOID, PERMISSIONS.SA],
    });

    const getUserPermissions = await cookieUser2.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMPANY,
        company_id: workspace_id,
        obj_type: 'user_permissions',
      }),
    );
    expect(getUserPermissions.status).toBe(RESP_STATUS.OK);
    expect(getUserPermissions.body.request_proc).toBe(PROC_STATUS.OK);
    const permissions = getUserPermissions.body.ops[0].permissions;
    expect(permissions).toHaveLength(3);
    expect(permissions).toContain('create_projects');
    expect(permissions).toContain('create_standalone_entities');

    await performSSRequest('workspace/user/perm/set', {
      id: TEST_USER_ID,
      perms: [PERMISSIONS.SA_WORKSPACE, PERMISSIONS.CONTROL_READONLY],
    });

    const getUserPermissionsGuest = await cookieUser2.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMPANY,
        company_id: workspace_id,
        obj_type: 'user_permissions',
      }),
    );
    expect(getUserPermissionsGuest.status).toBe(RESP_STATUS.OK);
    expect(getUserPermissionsGuest.body.request_proc).toBe(PROC_STATUS.OK);
    const permissionsGuest = getUserPermissionsGuest.body.ops[0].permissions;
    expect(permissionsGuest).toHaveLength(0);
  });
});
