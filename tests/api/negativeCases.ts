import faker from 'faker';

export const integerTestCases = [
  {
    input: {},
    errors: [
      `Value is not valid`,
      `Value '{[]}' is not valid. Type of value is not 'binary' or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '{[]}' is not valid. Type of value is not 'binary'`,
      `Value '{[]}' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `obj_id has not valid type, expected types [integer, binary_integer]`,
      `obj_type does not coincide with one of the expected values [user]`,
      `Value '{[]}' is not valid stage_id or Key 'stage_short_name' is required`,
      `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`,
      `Value '{[]}' is not valid. Type of value is not 'binary' or Value '<<\"[[{<<\\\"obj_id\\\">>,{[]}},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,{[]}},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,{[]}},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,{[]}},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,{[]}}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[2]\">>`,
      `conv_id has not valid type, expected types [integer, binary_integer]`,
      `folder_id has not valid type, expected types [non_neg_integer]`,
      `limit has not valid type, expected types [non_neg_integer]`,
      `offset has not valid type, expected types [non_neg_integer]`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`,
      `max threads must be int`,
      `Wrong object reference. Validation error`,
      `One of keys obj_id or obj_short_name is required`,
      `Key 'project_short_name' is required or Value '{[]}' is not valid project_id or Key 'folder_id' is required`,
      `Key 'obj_short_name' is required or Value is not valid`,
      `Key 'project_short_name' is required or Value '{[]}' is not valid project_id`,
      `Value is not valid or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `parent object key is not found or not valid. Parent key must be 'stage_id' or 'folder_id', and it must be integer type`,
      `Key 'obj_to' is required or Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value is not valid or Key 'obj_type' is required`,
      `limit has not valid type, expected types [integer]`,
      `project_id has not valid type, expected types [integer, binary_integer]`,
      `stage_id has not valid type, expected types [integer, binary_integer]`,
      `Key version_id. Value is not valid`,
      `Key stage_to_id. Value is not valid`,
      `Key project_id. Value is not valid`,
      `Couldn't convert value '[object Object]' for key 'conv_id' `,
      `Value is not valid or Key 'obj_to_login' is required`,
      `Value '{[]}' is not valid. Type of value is not 'binary' or Value '{[]}' is not valid. Type of value is not 'integer'`,
      `bad offset`,
      `bad limit`,
      `start has not valid type, expected types [integer, binary_integer]`,
      `end has not valid type, expected types [integer, binary_integer]`,
      `node_id has not valid type, expected types [binary]`,
      `{\"description\":\"Value '{[]}' is not valid. Type of value is not 'integer'\",\"key\":\"limit\",\"value\":{}}`,
      `{\"description\":\"Value '{[]}' is not valid. Type of value is not 'integer'\",\"key\":\"offset\",\"value\":{}}`,
      `{\"description\":\"Value '{[]}' is not valid. Type of value is not 'integer'\",\"key\":\"obj_id\",\"value\":{}}`,
      `{\"description\":\"Value is not valid\",\"key\":\"start_time\",\"value\":{}}`,
      `{\"description\":\"Value is not valid\",\"key\":\"expire_time\",\"value\":{}}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_active_procs\",\"value\":{}}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_rps\",\"value\":{}}`,
      `{\"description\":\"Value is not valid\",\"key\":\"min_timer\",\"value\":{}}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_process\",\"value\":{}}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_users\",\"value\":{}}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_storage_size\",\"value\":{}}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_state_diagram\",\"value\":{}}`,
    ],
  },
  {
    input: [],
    errors: [
      `Value is not valid`,
      `Value '[]' is not valid. Type of value is not 'binary' or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '[]' is not valid. Type of value is not 'binary'`,
      `Value '[]' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `obj_id has not valid type, expected types [integer, binary_integer]`,
      `obj_type does not coincide with one of the expected values [user]`,
      `Value '' is not valid stage_id or Key 'stage_short_name' is required`,
      `Value '' is not valid project_id or Key 'project_short_name' is required`,
      `Value '[]' is not valid. Type of value is not 'binary' or Value '<<\"[[{<<\\\"obj_id\\\">>,[]},{<<\\\"x\\\">>,0},{<<\\\"y\\\">>,0},{<<\\\"width\\\">>,4},{<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,[]},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,[]},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,[]},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,[]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[2]\">>`,
      `conv_id has not valid type, expected types [integer, binary_integer]`,
      `folder_id has not valid type, expected types [non_neg_integer]`,
      `limit has not valid type, expected types [non_neg_integer]`,
      `offset has not valid type, expected types [non_neg_integer]`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`,
      `max threads must be int`,
      `Wrong object reference. Validation error`,
      `One of keys obj_id or obj_short_name is required`,
      `Key 'project_short_name' is required or Value '' is not valid project_id or Key 'folder_id' is required`,
      `Key 'obj_short_name' is required or Value is not valid`,
      `Key 'project_short_name' is required or Value '' is not valid project_id`,
      `Value is not valid or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `parent object key is not found or not valid. Parent key must be 'stage_id' or 'folder_id', and it must be integer type`,
      `Key 'obj_to' is required or Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value is not valid or Key 'obj_type' is required`,
      `limit has not valid type, expected types [integer]`,
      `project_id has not valid type, expected types [integer, binary_integer]`,
      `stage_id has not valid type, expected types [integer, binary_integer]`,
      `Key version_id. Value is not valid`,
      `Key stage_to_id. Value is not valid`,
      `Key project_id. Value is not valid`,
      `Couldn't convert value '' for key 'conv_id' `,
      `Value is not valid or Key 'obj_to_login' is required`,
      `Value '[]' is not valid. Type of value is not 'binary' or Value '[]' is not valid. Type of value is not 'integer'`,
      `bad offset`,
      `bad limit`,
      `start has not valid type, expected types [integer, binary_integer]`,
      `end has not valid type, expected types [integer, binary_integer]`,
      `node_id has not valid type, expected types [binary]`,
      `{\"description\":\"Value '[]' is not valid. Type of value is not 'integer'\",\"key\":\"limit\",\"value\":[]}`,
      `{\"description\":\"Value '[]' is not valid. Type of value is not 'integer'\",\"key\":\"offset\",\"value\":[]}`,
      `{\"description\":\"Value '[]' is not valid. Type of value is not 'integer'\",\"key\":\"obj_id\",\"value\":[]}`,
      `{\"description\":\"Value is not valid\",\"key\":\"start_time\",\"value\":[]}`,
      `{\"description\":\"Value is not valid\",\"key\":\"expire_time\",\"value\":[]}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_active_procs\",\"value\":[]}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_rps\",\"value\":[]}`,
      `{\"description\":\"Value is not valid\",\"key\":\"min_timer\",\"value\":[]}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_process\",\"value\":[]}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_users\",\"value\":[]}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_storage_size\",\"value\":[]}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_state_diagram\",\"value\":[]}`,
    ],
  },
  {
    input: true,
    errors: [
      `Value is not valid`,
      `Value 'true' is not valid. Type of value is not 'binary' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value 'true' is not valid. Type of value is not 'binary'`,
      `Value 'true' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `obj_id has not valid type, expected types [integer, binary_integer]`,
      `obj_type does not coincide with one of the expected values [user]`,
      `Value 'true' is not valid stage_id or Key 'stage_short_name' is required`,
      `Value 'true' is not valid project_id or Key 'project_short_name' is required`,
      `Value 'true' is not valid. Type of value is not 'binary' or Value '<<\"[[{<<\\\"obj_id\\\">>,true},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,true},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,true},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,true},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,true}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[2]\">>`,
      `conv_id has not valid type, expected types [integer, binary_integer]`,
      `folder_id has not valid type, expected types [non_neg_integer]`,
      `limit has not valid type, expected types [non_neg_integer]`,
      `offset has not valid type, expected types [non_neg_integer]`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`,
      `max threads must be int`,
      `Wrong object reference. Validation error`,
      `One of keys obj_id or obj_short_name is required`,
      `Key 'project_short_name' is required or Value 'true' is not valid project_id or Key 'folder_id' is required`,
      `Key 'obj_short_name' is required or Value is not valid`,
      `Key 'project_short_name' is required or Value 'true' is not valid project_id`,
      `Stage of object do not matches stageId of request`,
      `Value is not valid or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `parent object key is not found or not valid. Parent key must be 'stage_id' or 'folder_id', and it must be integer type`,
      `Key 'obj_to' is required or Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value is not valid or Key 'obj_type' is required`,
      `limit has not valid type, expected types [integer]`,
      `project_id has not valid type, expected types [integer, binary_integer]`,
      `stage_id has not valid type, expected types [integer, binary_integer]`,
      `Key version_id. Value is not valid`,
      `Key stage_to_id. Value is not valid`,
      `Key project_id. Value is not valid`,
      `Couldn't convert value 'true' for key 'conv_id' `,
      `Value is not valid or Key 'obj_to_login' is required`,
      `Value 'true' is not valid. Type of value is not 'binary' or Value 'true' is not valid. Type of value is not 'integer'`,
      `bad offset`,
      `bad limit`,
      `start has not valid type, expected types [integer, binary_integer]`,
      `end has not valid type, expected types [integer, binary_integer]`,
      `node_id has not valid type, expected types [binary]`,
      `{\"description\":\"Value 'true' is not valid. Type of value is not 'integer'\",\"key\":\"limit\",\"value\":true}`,
      `{\"description\":\"Value 'true' is not valid. Type of value is not 'integer'\",\"key\":\"offset\",\"value\":true}`,
      `{\"description\":\"Value 'true' is not valid. Type of value is not 'integer'\",\"key\":\"obj_id\",\"value\":true}`,
      `{\"description\":\"Value is not valid\",\"key\":\"start_time\",\"value\":true}`,
      `{\"description\":\"Value is not valid\",\"key\":\"expire_time\",\"value\":true}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_active_procs\",\"value\":true}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_rps\",\"value\":true}`,
      `{\"description\":\"Value is not valid\",\"key\":\"min_timer\",\"value\":true}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_process\",\"value\":true}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_users\",\"value\":true}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_storage_size\",\"value\":true}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_state_diagram\",\"value\":true}`,
    ],
  },
  {
    input: 0,
    errors: [
      `Object dashboard with id 0 does not exist`,
      `Value '0' is not valid. Type of value is not 'binary' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '0' is not valid. Type of value is not 'binary'`,
      `This version hasn't any commit`,
      `Object conv with id 0 does not exist`,
      `Object instance with id 0 does not exist`,
      `object not found`,
      `Invalid dbcall instance`,
      `Object user with id 0 does not exist`,
      `Access denied`,
      `Object's company ID does not match company ID in the request`,
      `object is not deleted`,
      `Project or stage mismatch`,
      `Value '0' is not valid. Type of value is not 'binary' or Value '<<\"[[{<<\\\"obj_id\\\">>,0},{<<\\\"x\\\">>,0},{<<\\\"y\\\">>,0},{<<\\\"width\\\">>,4},{<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,0},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,0}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[2]\">>`,
      `conv_id has not valid type, expected types [integer, binary_integer]`,
      `offset has not valid type, expected types [non_neg_integer]`,
      `There is unconfirmed previous version`,
      `max threads must be int`,
      `conveyor_not_found`,
      `Object project with id 0 does not exist`,
      `Value is not valid. Value's limit is less than minimum allowed: 1`,
      `Wrong object reference. Validation error`,
      `Object alias not found`,
      `Key 'project_short_name' is required or Value '0' is not valid project_id or Key 'folder_id' is required`,
      `Object stage with id 0 does not exist`,
      `Value is not valid`,
      `Stage of object do not matches stageId of request`,
      `Value is not valid or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `parent object key is not found or not valid. Parent key must be 'stage_id' or 'folder_id', and it must be integer type`,
      `Object alias with id 0 does not exist`,
      `Alias not found`,
      `Key 'obj_to' is required or Value is not valid`,
      `Link failed: user not in the same company, what and specified folder.`,
      `Object folder with id 0 does not exist`,
      `limit has not valid type, expected types [integer]`,
      `project_id has not valid type, expected types [integer, binary_integer]`,
      `stage_id has not valid type, expected types [integer, binary_integer]`,
      `Key version_id. Value is not valid. Value's limit is less than minimum allowed: 1`,
      `Key stage_to_id. Value is not valid. Value's limit is less than minimum allowed: 1`,
      `Old owner are invalid. Reason: not found (note that object may be deleted)`,
      `User 0 not found.`,
      `Value '0' is not valid. Type of value is not 'binary' or Value is not valid. Value's limit is less than minimum allowed: 1`,
      `History not found`,
      `bad offset`,
      `bad limit`,
      `node_id has not valid type, expected types [binary]`,
      `license is not found`,
      `{\"proc\":\"error\",\"description\":\"license is not found\"}`,
      `Project of object do not matches projectId of request`,
      `{\"obj_type\":\"user\",\"obj_id\":0,\"corezoid_id\":\"not_found\",\"account_id\":\"not_found\"}`,
    ],
  },
  {
    input: -1,
    errors: [
      `Value is not valid. Value's limit is less than minimum allowed: 0`,
      `Value '-1' is not valid. Type of value is not 'binary' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '-1' is not valid. Type of value is not 'binary'`,
      `Object dashboard with id -1 does not exist`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid. Value's limit is less than minimum allowed: 0`,
      `This version hasn't any commit`,
      `obj_id has not valid type, expected types [integer, binary_integer]`,
      `Object instance with id -1 does not exist`,
      `Object user with id -1 does not exist`,
      `object is not deleted`,
      `Object folder with id -1 does not exist`,
      `Value '-1' is not valid. Type of value is not 'binary' or Value '<<\"[[{<<\\\"obj_id\\\">>,-1},{<<\\\"x\\\">>,0},{<<\\\"y\\\">>,0},{<<\\\"width\\\">>,4},{<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,-1},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,-1},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,-1},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,-1}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[2]\">>`,
      `Object conv with id -1 does not exist`,
      `Value '-1' is not valid project_id or Key 'project_short_name' is required`,
      `folder_id has not valid type, expected types [non_neg_integer]`,
      `limit has not valid type, expected types [non_neg_integer]`,
      `offset has not valid type, expected types [non_neg_integer]`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`,
      `There is unconfirmed previous version`,
      `max threads must be int`,
      `conveyor_not_found`,
      `One of keys obj_id or obj_short_name is required`,
      `Value is not valid. Value's limit is less than minimum allowed: 1`,
      `Wrong object reference. Validation error`,
      `Object alias not found`,
      `Key 'project_short_name' is required or Value '-1' is not valid project_id or Key 'folder_id' is required`,
      `Key 'obj_short_name' is required or Value is not valid. Value's limit is less than minimum allowed: 0`,
      `Key 'project_short_name' is required or Value '-1' is not valid project_id`,
      `Value is not valid`,
      `Value is not valid or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Project or stage mismatch`,
      `Stage not found`,
      `parent_id has not valid type, expected types [non_neg_integer, binary_integer]`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value is not valid. Value's limit is less than minimum allowed: 0 or Key 'obj_type' is required`,
      `Link failed: user not in the same company, what and specified folder.`,
      `limit has not valid type, expected types [integer]`,
      `project_id has not valid type, expected types [integer, binary_integer]`,
      `stage_id has not valid type, expected types [integer, binary_integer]`,
      `limit less than minimum allowed size (range) 1`,
      `project_id less than minimum allowed size (range) 1`,
      `stage_id less than minimum allowed size (range) 1`,
      `Key version_id. Value is not valid. Value's limit is less than minimum allowed: 1`,
      `Key stage_to_id. Value is not valid. Value's limit is less than minimum allowed: 1`,
      `Value is not valid. Value's limit is less than minimum allowed: 0 or Key 'obj_to_login' is required`,
      `Value '-1' is not valid. Type of value is not 'binary' or Value is not valid. Value's limit is less than minimum allowed: 1`,
      `bad offset`,
      `bad limit`,
      `node_id has not valid type, expected types [binary]`,
      `{\"description\":\"Value is not valid. Value's limit is less than minimum allowed: 0\",\"key\":\"limit\",\"value\":-1}`,
      `{\"description\":\"Value is not valid. Value's limit is less than minimum allowed: 0\",\"key\":\"offset\",\"value\":-1}`,
      `license is not found`,
      `{\"proc\":\"error\",\"description\":\"license is not found\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"start_time\",\"value\":-1}`,
      `{\"description\":\"Value is not valid\",\"key\":\"expire_time\",\"value\":-1}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_active_procs\",\"value\":-1}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_rps\",\"value\":-1}`,
      `{\"description\":\"Value is not valid\",\"key\":\"min_timer\",\"value\":-1}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_process\",\"value\":-1}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_users\",\"value\":-1}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_storage_size\",\"value\":-1}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_state_diagram\",\"value\":-1}`,
      `Stage of object do not matches stageId of request`,
      `{"obj_type":"user","obj_id":-1,"corezoid_id":"not_found","account_id":"not_found"}`,
    ],
  },
  {
    input: null,
    errors: [
      `Value is not valid`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value 'null' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `obj_id has not valid type, expected types [integer, binary_integer]`,
      `obj_type does not coincide with one of the expected values [user]`,
      `Value 'null' is not valid stage_id or Key 'stage_short_name' is required`,
      `Value 'null' is not valid project_id or Key 'project_short_name' is required`,
      `Value 'null' is not valid. Type of value is not 'binary' or Value '<<\"[[{<<\\\"obj_id\\\">>,null},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,null},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,null},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,null},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,null}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[2]\">>`,
      `conv_id has not valid type, expected types [integer, binary_integer]`,
      `folder_id has not valid type, expected types [non_neg_integer]`,
      `limit has not valid type, expected types [non_neg_integer]`,
      `offset has not valid type, expected types [non_neg_integer]`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`,
      `max threads must be int`,
      `Wrong object reference. Validation error`,
      `One of keys obj_id or obj_short_name is required`,
      `Key 'project_short_name' is required or Value 'null' is not valid project_id or Key 'folder_id' is required`,
      `Key 'obj_short_name' is required or Value is not valid`,
      `Key 'project_short_name' is required or Value 'null' is not valid project_id`,
      `parent object key is not found or not valid. Parent key must be 'stage_id' or 'folder_id', and it must be integer type`,
      `Key 'obj_to' is required or Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value is not valid or Key 'obj_type' is required`,
      `limit has not valid type, expected types [integer]`,
      `project_id has not valid type, expected types [integer, binary_integer]`,
      `stage_id has not valid type, expected types [integer, binary_integer]`,
      `Key version_id. Value is not valid`,
      `Key stage_to_id. Value is not valid`,
      `Key project_id. Value is not valid`,
      `Couldn't convert value 'null' for key 'conv_id' `,
      `Value is not valid or Key 'obj_to_login' is required`,
      `Value 'null' is not valid. Type of value is not 'binary' or Value 'null' is not valid. Type of value is not 'integer'`,
      `bad offset`,
      `bad limit`,
      `start has not valid type, expected types [integer, binary_integer]`,
      `end has not valid type, expected types [integer, binary_integer]`,
      `node_id has not valid type, expected types [binary]`,
      `{\"description\":\"Value 'null' is not valid. Type of value is not 'integer'\",\"key\":\"limit\",\"value\":null}`,
      `{\"description\":\"Value 'null' is not valid. Type of value is not 'integer'\",\"key\":\"offset\",\"value\":null}`,
      `{\"description\":\"Value 'null' is not valid. Type of value is not 'integer'\",\"key\":\"obj_id\",\"value\":null}`,
      `{\"description\":\"Value is not valid\",\"key\":\"start_time\",\"value\":null}`,
      `{\"description\":\"Value is not valid\",\"key\":\"expire_time\",\"value\":null}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_active_procs\",\"value\":null}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_rps\",\"value\":null}`,
      `{\"description\":\"Value is not valid\",\"key\":\"min_timer\",\"value\":null}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_process\",\"value\":null}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_users\",\"value\":null}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_storage_size\",\"value\":null}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_state_diagram\",\"value\":null}`,
    ],
  },
  {
    input: 'test',
    errors: [
      `Value is not valid`,
      `Value is not valid uuid-v4 value or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24`,
      `Value 'test' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `obj_id has not valid type, expected types [integer, binary_integer]`,
      `obj_type does not coincide with one of the expected values [user]`,
      `Value 'test' is not valid stage_id or Key 'stage_short_name' is required`,
      `Value 'test' is not valid project_id or Key 'project_short_name' is required`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"test\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,<<\\\"test\\\">>},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,<<\\\"test\\\">>},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,<<\\\"test\\\">>},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,<<\\\"test\\\">>}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[2]\">>`,
      `conv_id has not valid type, expected types [integer, binary_integer]`,
      `folder_id has not valid type, expected types [non_neg_integer]`,
      `limit has not valid type, expected types [non_neg_integer]`,
      `offset has not valid type, expected types [non_neg_integer]`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`,
      `max threads must be int`,
      `Wrong object reference. Validation error`,
      `One of keys obj_id or obj_short_name is required`,
      `Wrong object linked to alias`,
      `Key 'project_short_name' is required or Value 'test' is not valid project_id or Key 'folder_id' is required`,
      `Key 'obj_short_name' is required or Value is not valid`,
      `Key 'project_short_name' is required or Value 'test' is not valid project_id`,
      `Value is not valid or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `parent object key is not found or not valid. Parent key must be 'stage_id' or 'folder_id', and it must be integer type`,
      `Key 'obj_to' is required or Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value is not valid or Key 'obj_type' is required`,
      `limit has not valid type, expected types [integer]`,
      `project_id has not valid type, expected types [integer, binary_integer]`,
      `stage_id has not valid type, expected types [integer, binary_integer]`,
      `Key version_id. Value is not valid`,
      `Key stage_to_id. Value is not valid`,
      `Key project_id. Value is not valid`,
      `Couldn't convert value 'test' for key 'conv_id' `,
      `Value is not valid or Key 'obj_to_login' is required`,
      `bad offset`,
      `bad limit`,
      `start has not valid type, expected types [integer, binary_integer]`,
      `end has not valid type, expected types [integer, binary_integer]`,
      `{\"description\":\"Value 'test' is not valid. Type of value is not 'integer'\",\"key\":\"limit\",\"value\":\"test\"}`,
      `{\"description\":\"Value 'test' is not valid. Type of value is not 'integer'\",\"key\":\"offset\",\"value\":\"test\"}`,
      `{\"description\":\"Value 'test' is not valid. Type of value is not 'integer'\",\"key\":\"obj_id\",\"value\":\"test\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"start_time\",\"value\":\"test\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"expire_time\",\"value\":\"test\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_active_procs\",\"value\":\"test\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_rps\",\"value\":\"test\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"min_timer\",\"value\":\"test\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_process\",\"value\":\"test\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_users\",\"value\":\"test\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_storage_size\",\"value\":\"test\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_state_diagram\",\"value\":\"test\"}`,
      `Invalid key obj_id test when obj_type is user`,
    ],
  },
  {
    input: '',
    errors: [
      `Value is not valid`,
      `Value is not valid uuid-v4 value or Value '<<>>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `obj_id has not valid type, expected types [integer, binary_integer]`,
      `obj_type does not coincide with one of the expected values [user]`,
      `Value '' is not valid stage_id or Key 'stage_short_name' is required`,
      `Value '' is not valid project_id or Key 'project_short_name' is required`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<>>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,<<>>},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,<<>>},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,<<>>},\\n  {<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},\\n  {<<\\\"x\\\">>,0},\\n  {<<\\\"y\\\">>,0},\\n  {<<\\\"width\\\">>,4},\\n  {<<\\\"height\\\">>,<<>>}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[2]\">>`,
      `conv_id has not valid type, expected types [integer, binary_integer]`,
      `folder_id has not valid type, expected types [non_neg_integer]`,
      `limit has not valid type, expected types [non_neg_integer]`,
      `offset has not valid type, expected types [non_neg_integer]`,
      `Value '' is not valid. Type of value is not 'integer'`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`,
      `max threads must be int`,
      `Wrong object reference. Validation error`,
      `One of keys obj_id or obj_short_name is required`,
      `Key 'project_short_name' is required or Value '' is not valid project_id or Key 'folder_id' is required`,
      `Key 'obj_short_name' is required or Value is not valid`,
      `Key 'project_short_name' is required or Value '' is not valid project_id`,
      `Value is not valid or Value '<<>>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `parent object key is not found or not valid. Parent key must be 'stage_id' or 'folder_id', and it must be integer type`,
      `Key 'obj_to' is required or Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value is not valid or Key 'obj_type' is required`,
      `limit has not valid type, expected types [integer]`,
      `project_id has not valid type, expected types [integer, binary_integer]`,
      `stage_id has not valid type, expected types [integer, binary_integer]`,
      `Key version_id. Value is not valid`,
      `Key stage_to_id. Value is not valid`,
      `Key project_id. Value is not valid`,
      `Couldn't convert value '' for key 'conv_id' `,
      `Value is not valid or Key 'obj_to_login' is required`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 1 or Value '' is not valid. Type of value is not 'integer'`,
      `bad offset`,
      `bad limit`,
      `start has not valid type, expected types [integer, binary_integer]`,
      `end has not valid type, expected types [integer, binary_integer]`,
      `{\"description\":\"Value '' is not valid. Type of value is not 'integer'\",\"key\":\"limit\",\"value\":\"\"}`,
      `{\"description\":\"Value '' is not valid. Type of value is not 'integer'\",\"key\":\"offset\",\"value\":\"\"}`,
      `{\"description\":\"Value '' is not valid. Type of value is not 'integer'\",\"key\":\"obj_id\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"start_time\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"expire_time\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_active_procs\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_rps\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"min_timer\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_process\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_users\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_storage_size\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_state_diagram\",\"value\":\"\"}`,
    ],
  },
];

export const stringTestCases = [
  {
    input: true,
    errors: [
      `Value 'true' is not valid. Type of value is not 'binary'`,
      `Value 'true' is not valid. Type of value is not 'binary' or Key 'sort' is required`,
      `Value 'true' is not valid. Type of value is not 'binary' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value is not valid`,
      `sql: ping: pq: SSL is not enabled on the server`,
      `obj_type does not coincide with one of the expected values [user]`,
      `type has not valid type, expected types [binary]`,
      `Value is not valid email`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"admins\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"actived\\\">>,<<\\\"not_actived\\\">>,<<\\\"deleted\\\">>]\">>`,
      `Params are not valid`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"basic\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function_build\\\">>]\">>`,
      `privs are not valid`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>,<<\\\"function_build\\\">>,<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `pattern has not valid type, expected types [binary]`,
      `from_node_id has not valid type, expected types [binary]`,
      `to_node_id has not valid type, expected types [binary]`,
      `obj_id has not valid type, expected types [binary]`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `token has not valid type, expected types [binary]`,
      `callback_hash has not valid type, expected types [binary]`,
      `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      `obj_type has not valid type, expected types [binary]`,
      `result has not valid type, expected types [binary]`,
      `description has not valid type, expected types [binary]`,
      `Wrong object reference. Validation error`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `invalid timer value`,
      `to small timer value, min must be 10`,
      `Unexpected logic type`,
      `invalid conditions`,
      `invalid ConvId`,
      `invalid mode`,
      `check result data types`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `One of keys obj_id or obj_short_name is required`,
      `task not found`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value 'true' is not valid. Type of value is not 'binary' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required or Key 'folder_id' is required`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'obj_id' is required`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
      `Project of object do not matches projectId of request`,
      `Stage not found`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"uncommited\\\">>]\">>`,
      `Value 'true' is not valid project_id or Key 'project_short_name' is required`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Couldnt link true to alias or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Key 'obj_to' is required or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">> or Key 'obj_type' is required`,
      `Link failed: user not in the same company, what and specified folder.`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `Couldnt link true to alias`,
      `Key 'conv_id' is required`,
      `Project or stage mismatch`,
      `project_short_name has not valid type, expected types [binary]`,
      `stage_short_name has not valid type, expected types [binary]`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,true},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
      `Value is not valid or Key 'obj_to_login' is required`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
      `Key 'obj_to_id' is required or Value 'true' is not valid. Type of value is not 'binary'`,
      `key: data.entity_type_name.Value 'true' is not valid. Type of value is not 'binary'`,
      `Key 'start' is required`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"week\\\">>,<<\\\"month\\\">>]\">>`,
      `group does not coincide with one of the expected values [all, time]`,
      `interval does not coincide with one of the expected values [hour, minute, day]`,
      `object does not exists`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"file\\\">>,<<\\\"server\\\">>]\">>`,
      `{\"description\":\"Value '<<\\\"true\\\">>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":true}`,
      `{\"description\":\"Value 'true' is not valid. Type of value is not 'binary'\",\"key\":\"company_name\",\"value\":true}`,
      `{\"description\":\"Value 'true' is not valid. Type of value is not 'binary'\",\"key\":\"cluster_id\",\"value\":true}`,
      `{\"description\":\"Value 'true' is not valid. Type of value is not 'binary'\",\"key\":\"name\",\"value\":true}`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"without_nodes\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>,<<\\\"escalation\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
      `Stage of object do not matches stageId of request`,
      `Key 'true' not found in config`,
      `Value 'null' is not valid for type 'boolean'`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"binary\\\">>,<<\\\"integer\\\">>,<<\\\"positive_integer\\\">>,<<\\\"atom\\\">>,<<\\\"json\\\">>,\\n <<\\\"float\\\">>,<<\\\"string\\\">>,<<\\\"boolean\\\">>]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
      `Bad object 'values'`,
      `Bad object 'true'`,
      `Object's company ID does not match company ID in the request`,
      `Key 'scopes_filter' is invalid, allowed only *, api_call, set_param, go_if_const, api_copy, db_call, api_rpc, api_rpc_reply, api_code`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"explain\\\">>,<<\\\"completion\\\">>]\">>`,
      `timezone_offset has not valid type, expected types [integer, binary_integer]`,
      `invalid extra types`,
      `invalid value result data types`,
    ],
  },
  {
    input: 0,
    errors: [
      `Value '0' is not valid. Type of value is not 'binary'`,
      `Value '0' is not valid. Type of value is not 'binary' or Key 'sort' is required`,
      `Value '0' is not valid. Type of value is not 'binary'", "Value '0' is not valid. Type of value is not 'binary' or Key 'sort' is required`,
      `Value '0' is not valid. Type of value is not 'binary' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value is not valid`,
      `Value is not valid. Value's limit is less than minimum allowed: 1`,
      `sql: ping: pq: SSL is not enabled on the server`,
      `obj_type does not coincide with one of the expected values [user]`,
      `type has not valid type, expected types [binary]`,
      `Value is not valid email`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"admins\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"actived\\\">>,<<\\\"not_actived\\\">>,<<\\\"deleted\\\">>]\">>`,
      `Params are not valid`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"basic\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function_build\\\">>]\">>`,
      `privs are not valid`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>,<<\\\"function_build\\\">>,<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `pattern has not valid type, expected types [binary]`,
      `from_node_id has not valid type, expected types [binary]`,
      `to_node_id has not valid type, expected types [binary]`,
      `obj_id has not valid type, expected types [binary]`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `token has not valid type, expected types [binary]`,
      `callback_hash has not valid type, expected types [binary]`,
      `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      `obj_type has not valid type, expected types [binary]`,
      `result has not valid type, expected types [binary]`,
      `description has not valid type, expected types [binary]`,
      `Object conv with id 0 does not exist`,
      `Wrong object reference. Validation error`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `cant create timer to itself`,
      `to small timer value, min must be 10`,
      `Unexpected logic type`,
      `invalid conditions`,
      `Only active process can be used`,
      `invalid mode`,
      `check result data types`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `One of keys obj_id or obj_short_name is required`,
      `task not found`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value '0' is not valid. Type of value is not 'binary' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required or Key 'folder_id' is required`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'obj_id' is required`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
      `Project of object do not matches projectId of request`,
      `Stage not found`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"uncommited\\\">>]\">>`,
      `object does not exists`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Project or stage mismatch`,
      `Couldnt link 0 to alias or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Key 'obj_to' is required or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">> or Key 'obj_type' is required`,
      `Link failed: user not in the same company, what and specified folder.`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `Couldnt link 0 to alias`,
      `Key 'conv_id' is required`,
      `Project or stage mismatch`,
      `project_short_name has not valid type, expected types [binary]`,
      `stage_short_name has not valid type, expected types [binary]`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,0},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
      `Key 'obj_to_id' is required or Value '0' is not valid. Type of value is not 'binary'`,
      `key: data.entity_type_name.Value '0' is not valid. Type of value is not 'binary'`,
      `Key 'start' is required`,
      `group does not coincide with one of the expected values [all, time]`,
      `interval does not coincide with one of the expected values [hour, minute, day]`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"file\\\">>,<<\\\"server\\\">>]\">>`,
      `{\"description\":\"Value '<<\\\"0\\\">>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":0}`,
      `{\"description\":\"Value '0' is not valid. Type of value is not 'binary'\",\"key\":\"company_name\",\"value\":0}`,
      `{\"description\":\"Value '0' is not valid. Type of value is not 'binary'\",\"key\":\"cluster_id\",\"value\":0}`,
      `{\"description\":\"Value '0' is not valid. Type of value is not 'binary'\",\"key\":\"name\",\"value\":0}`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"without_nodes\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>,<<\\\"escalation\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
      `Stage of object do not matches stageId of request`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>]\">> or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"active\\\">>,<<\\\"paused\\\">>,<<\\\"debug\\\">>,<<\\\"active_debug\\\">>]\">>`,
      `Key '0' not found in config`,
      `Value '0' is not valid for type 'boolean'`,
      `Value 'null' is not valid for type 'boolean'`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"binary\\\">>,<<\\\"integer\\\">>,<<\\\"positive_integer\\\">>,<<\\\"atom\\\">>,<<\\\"json\\\">>,\\n <<\\\"float\\\">>,<<\\\"string\\\">>,<<\\\"boolean\\\">>]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
      `Value '<<\"new_user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '0' is not valid. Type of value is not 'binary' or Value '<<\"new_user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Bad object 'values'`,
      `Bad object '0'`,
      `Object's company ID does not match company ID in the request`,
      `Key 'scopes_filter' is invalid, allowed only *, api_call, set_param, go_if_const, api_copy, db_call, api_rpc, api_rpc_reply, api_code`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"explain\\\">>,<<\\\"completion\\\">>]\">>`,
      `invalid extra types`,
      `invalid value result data types`,
    ],
  },
  {
    input: {},
    errors: [
      `Value '{[]}' is not valid. Type of value is not 'binary'`,
      `Value '{[]}' is not valid. Type of value is not 'binary' or Key 'sort' is required`,
      `Value '{[]}' is not valid. Type of value is not 'binary' or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value is not valid`,
      `obj_type does not coincide with one of the expected values [user]`,
      `type has not valid type, expected types [binary]`,
      `Value is not valid email`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"admins\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"actived\\\">>,<<\\\"not_actived\\\">>,<<\\\"deleted\\\">>]\">>`,
      `Params are not valid`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"basic\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function_build\\\">>]\">>`,
      `privs are not valid`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>,<<\\\"function_build\\\">>,<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `pattern has not valid type, expected types [binary]`,
      `from_node_id has not valid type, expected types [binary]`,
      `to_node_id has not valid type, expected types [binary]`,
      `obj_id has not valid type, expected types [binary]`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `token has not valid type, expected types [binary]`,
      `callback_hash has not valid type, expected types [binary]`,
      `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      `obj_type has not valid type, expected types [binary]`,
      `result has not valid type, expected types [binary]`,
      `description has not valid type, expected types [binary]`,
      `Wrong object reference. Validation error`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `invalid timer value`,
      `to small timer value, min must be 10`,
      `Unexpected logic type`,
      `invalid conditions`,
      `invalid ConvId`,
      `invalid mode`,
      `check result data types`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `One of keys obj_id or obj_short_name is required`,
      `task not found`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value '{[]}' is not valid. Type of value is not 'binary' or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required or Key 'folder_id' is required`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'obj_id' is required`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
      `Project of object do not matches projectId of request`,
      `Stage not found`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"uncommited\\\">>]\">>`,
      `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Couldnt link {[]} to alias or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Key 'obj_to' is required or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">> or Key 'obj_type' is required`,
      `Link failed: user not in the same company, what and specified folder.`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `Couldnt link {[]} to alias`,
      `Key 'conv_id' is required`,
      `Project or stage mismatch`,
      `project_short_name has not valid type, expected types [binary]`,
      `stage_short_name has not valid type, expected types [binary]`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,{[]}},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
      `Key 'obj_to_id' is required or Value '{[]}' is not valid. Type of value is not 'binary'`,
      `key: data.entity_type_name.Value '{[]}' is not valid. Type of value is not 'binary'`,
      `Key 'start' is required`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"week\\\">>,<<\\\"month\\\">>]\">>`,
      `group does not coincide with one of the expected values [all, time]`,
      `interval does not coincide with one of the expected values [hour, minute, day]`,
      `object does not exists`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"file\\\">>,<<\\\"server\\\">>]\">>`,
      `{\"description\":\"Value '<<\\\"{[]}\\\">>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":{}}`,
      `{\"description\":\"Value '<<\\\"0\\\">>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":0}`,
      `{\"description\":\"Value '{[]}' is not valid. Type of value is not 'binary'\",\"key\":\"company_name\",\"value\":{}}`,
      `{\"description\":\"Value '{[]}' is not valid. Type of value is not 'binary'\",\"key\":\"cluster_id\",\"value\":{}}`,
      `{\"description\":\"Value '{[]}' is not valid. Type of value is not 'binary'\",\"key\":\"name\",\"value\":{}}`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"without_nodes\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>,<<\\\"escalation\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
      `Stage of object do not matches stageId of request`,
      `Couldn't convert value '{[]}' for key 'key' `,
      `Value '{[]}' is not valid for type 'boolean'`,
      `Value 'null' is not valid for type 'boolean'`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"binary\\\">>,<<\\\"integer\\\">>,<<\\\"positive_integer\\\">>,<<\\\"atom\\\">>,<<\\\"json\\\">>,\\n <<\\\"float\\\">>,<<\\\"string\\\">>,<<\\\"boolean\\\">>]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
      `Bad object 'values'`,
      `Bad object '{[]}'`,
      `Object's company ID does not match company ID in the request`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"explain\\\">>,<<\\\"completion\\\">>]\">>`,
      `timezone_offset has not valid type, expected types [integer, binary_integer]`,
      `invalid extra types`,
      `invalid value result data types`,
    ],
  },
  {
    input: [],
    errors: [
      `Value '[]' is not valid. Type of value is not 'binary'`,
      `Value '[]' is not valid. Type of value is not 'binary' or Key 'sort' is required`,
      `Value '[]' is not valid. Type of value is not 'binary' or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value is not valid`,
      `obj_type does not coincide with one of the expected values [user]`,
      `type has not valid type, expected types [binary]`,
      `Value is not valid email`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"admins\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"actived\\\">>,<<\\\"not_actived\\\">>,<<\\\"deleted\\\">>]\">>`,
      `Params are not valid`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"basic\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function_build\\\">>]\">>`,
      `privs are not valid`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>,<<\\\"function_build\\\">>,<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `pattern has not valid type, expected types [binary]`,
      `from_node_id has not valid type, expected types [binary]`,
      `to_node_id has not valid type, expected types [binary]`,
      `obj_id has not valid type, expected types [binary]`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `token has not valid type, expected types [binary]`,
      `callback_hash has not valid type, expected types [binary]`,
      `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      `obj_type has not valid type, expected types [binary]`,
      `result has not valid type, expected types [binary]`,
      `description has not valid type, expected types [binary]`,
      `Wrong object reference. Validation error`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `invalid timer value`,
      `to small timer value, min must be 10`,
      `Unexpected logic type`,
      `invalid conditions`,
      `invalid ConvId`,
      `invalid mode`,
      `check result data types`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `One of keys obj_id or obj_short_name is required`,
      `task not found`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value '[]' is not valid. Type of value is not 'binary' or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required or Key 'folder_id' is required`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'obj_id' is required`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
      `Project of object do not matches projectId of request`,
      `Stage not found`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"uncommited\\\">>]\">>`,
      `Value '' is not valid project_id or Key 'project_short_name' is required`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Couldnt link  to alias or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Key 'obj_to' is required or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">> or Key 'obj_type' is required`,
      `Link failed: user not in the same company, what and specified folder.`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `Couldnt link  to alias`,
      `Key 'conv_id' is required`,
      `Project or stage mismatch`,
      `project_short_name has not valid type, expected types [binary]`,
      `stage_short_name has not valid type, expected types [binary]`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,[]},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
      `Key 'obj_to_id' is required or Value '[]' is not valid. Type of value is not 'binary'`,
      `key: data.entity_type_name.Value '[]' is not valid. Type of value is not 'binary'`,
      `Key 'start' is required`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"week\\\">>,<<\\\"month\\\">>]\">>`,
      `group does not coincide with one of the expected values [all, time]`,
      `interval does not coincide with one of the expected values [hour, minute, day]`,
      `object does not exists`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"file\\\">>,<<\\\"server\\\">>]\">>`,
      `{\"description\":\"Value '<<\\\"[]\\\">>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":[]}`,
      `{\"description\":\"Value '[]' is not valid. Type of value is not 'binary'\",\"key\":\"company_name\",\"value\":[]}`,
      `{\"description\":\"Value '[]' is not valid. Type of value is not 'binary'\",\"key\":\"cluster_id\",\"value\":[]}`,
      `{\"description\":\"Value '[]' is not valid. Type of value is not 'binary'\",\"key\":\"name\",\"value\":[]}`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"without_nodes\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>,<<\\\"escalation\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
      `Stage of object do not matches stageId of request`,
      `Key '' not found in config`,
      `Value '' is not valid for type 'boolean'`,
      `Value 'null' is not valid for type 'boolean'`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"binary\\\">>,<<\\\"integer\\\">>,<<\\\"positive_integer\\\">>,<<\\\"atom\\\">>,<<\\\"json\\\">>,\\n <<\\\"float\\\">>,<<\\\"string\\\">>,<<\\\"boolean\\\">>]\">>`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
      `Bad object 'values'`,
      `Bad object ''`,
      `Object's company ID does not match company ID in the request`,
      `Key 'scopes_filter' is empty`,
      `Key 'scopes_filter' is invalid, allowed only *, api_call, set_param, go_if_const, api_copy, db_call, api_rpc, api_rpc_reply, api_code`,
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"explain\\\">>,<<\\\"completion\\\">>]\">>`,
      `timezone_offset has not valid type, expected types [integer, binary_integer]`,
      `invalid extra types`,
      `invalid value result data types`,
    ],
  },
  {
    input: -1,
    errors: [
      `Value '-1' is not valid. Type of value is not 'binary'`,
      `Value '-1' is not valid. Type of value is not 'binary' or Key 'sort' is required`,
      `Value '-1' is not valid. Type of value is not 'binary' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value is not valid`,
      `Value is not valid. Value's limit is less than minimum allowed: 1`,
      `sql: ping: pq: SSL is not enabled on the server`,
      `obj_type does not coincide with one of the expected values [user]`,
      `type has not valid type, expected types [binary]`,
      `Value is not valid email`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"admins\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"actived\\\">>,<<\\\"not_actived\\\">>,<<\\\"deleted\\\">>]\">>`,
      `Params are not valid`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"basic\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function_build\\\">>]\">>`,
      `privs are not valid`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>,<<\\\"function_build\\\">>,<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `pattern has not valid type, expected types [binary]`,
      `from_node_id has not valid type, expected types [binary]`,
      `to_node_id has not valid type, expected types [binary]`,
      `obj_id has not valid type, expected types [binary]`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `token has not valid type, expected types [binary]`,
      `callback_hash has not valid type, expected types [binary]`,
      `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      `obj_type has not valid type, expected types [binary]`,
      `result has not valid type, expected types [binary]`,
      `description has not valid type, expected types [binary]`,
      `Object conv with id -1 does not exist`,
      `Wrong object reference. Validation error`,
      `Value is not valid. Value's limit is less than minimum allowed: 0`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `cant create timer to itself`,
      `to small timer value, min must be 10`,
      `Unexpected logic type`,
      `invalid conditions`,
      `Only active process can be used`,
      `invalid mode`,
      `check result data types`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `One of keys obj_id or obj_short_name is required`,
      `task not found`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value '-1' is not valid. Type of value is not 'binary' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required or Key 'folder_id' is required`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'obj_id' is required`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
      `Project of object do not matches projectId of request`,
      `Stage not found`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"uncommited\\\">>]\">>`,
      `Value '-1' is not valid project_id or Key 'project_short_name' is required`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Couldnt link -1 to alias or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Key 'obj_to' is required or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">> or Key 'obj_type' is required`,
      `Link failed: user not in the same company, what and specified folder.`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `Couldnt link -1 to alias`,
      `Key 'conv_id' is required`,
      `Project or stage mismatch`,
      `project_short_name has not valid type, expected types [binary]`,
      `stage_short_name has not valid type, expected types [binary]`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,-1},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>},{<<\\\"list_obj\\\">>,-1}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
      `Key 'obj_to_id' is required or Value '-1' is not valid. Type of value is not 'binary'`,
      `key: data.entity_type_name.Value '-1' is not valid. Type of value is not 'binary'`,
      `Key 'start' is required`,
      `group does not coincide with one of the expected values [all, time]`,
      `interval does not coincide with one of the expected values [hour, minute, day]`,
      `object does not exists`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"file\\\">>,<<\\\"server\\\">>]\">>`,
      `{\"description\":\"Value '<<\\\"-1\\\">>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":-1}`,
      `{\"description\":\"Value '-1' is not valid. Type of value is not 'binary'\",\"key\":\"company_name\",\"value\":-1}`,
      `{\"description\":\"Value '-1' is not valid. Type of value is not 'binary'\",\"key\":\"cluster_id\",\"value\":-1}`,
      `{\"description\":\"Value '-1' is not valid. Type of value is not 'binary'\",\"key\":\"name\",\"value\":-1}`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"without_nodes\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>,<<\\\"escalation\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
      `Stage of object do not matches stageId of request`,
      `Key '-1' not found in config`,
      `Value '-1' is not valid for type 'boolean'`,
      `Value 'null' is not valid for type 'boolean'`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"binary\\\">>,<<\\\"integer\\\">>,<<\\\"positive_integer\\\">>,<<\\\"atom\\\">>,<<\\\"json\\\">>,\\n <<\\\"float\\\">>,<<\\\"string\\\">>,<<\\\"boolean\\\">>]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
      `Bad object 'values'`,
      `Bad object '-1'`,
      `Object's company ID does not match company ID in the request`,
      `Key 'scopes_filter' is invalid, allowed only *, api_call, set_param, go_if_const, api_copy, db_call, api_rpc, api_rpc_reply, api_code`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"explain\\\">>,<<\\\"completion\\\">>]\">>`,
      `invalid extra types`,
      `invalid value result data types`,
    ],
  },
  {
    input: 1,
    errors: [
      `Value '1' is not valid. Type of value is not 'binary'`,
      `Value '1' is not valid. Type of value is not 'binary' or Key 'sort' is required`,
      `Value '1' is not valid. Type of value is not 'binary' or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value is not valid`,
      `sql: ping: pq: SSL is not enabled on the server`,
      `obj_type does not coincide with one of the expected values [user]`,
      `type has not valid type, expected types [binary]`,
      `Value is not valid email`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"admins\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"actived\\\">>,<<\\\"not_actived\\\">>,<<\\\"deleted\\\">>]\">>`,
      `Params are not valid`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"basic\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function_build\\\">>]\">>`,
      `privs are not valid`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>,<<\\\"function_build\\\">>,<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `pattern has not valid type, expected types [binary]`,
      `from_node_id has not valid type, expected types [binary]`,
      `to_node_id has not valid type, expected types [binary]`,
      `obj_id has not valid type, expected types [binary]`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `token has not valid type, expected types [binary]`,
      `callback_hash has not valid type, expected types [binary]`,
      `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      `obj_type has not valid type, expected types [binary]`,
      `result has not valid type, expected types [binary]`,
      `description has not valid type, expected types [binary]`,
      `Object's company ID does not match company ID in the request`,
      `Wrong object reference. Validation error`,
      `User has no rights`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `cant create timer to itself`,
      `to small timer value, min must be 10`,
      `Unexpected logic type`,
      `invalid conditions`,
      `invalid mode`,
      `check result data types`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `One of keys obj_id or obj_short_name is required`,
      `task not found`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value '1' is not valid. Type of value is not 'binary' or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required or Key 'folder_id' is required`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'obj_id' is required`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
      `Project of object do not matches projectId of request`,
      `Stage not found`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"uncommited\\\">>]\">>`,
      `object does not exists`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Project or stage mismatch`,
      `Couldnt link null to alias or Value '<<\"1634410\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Couldnt link 1 to alias or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Key 'obj_to' is required or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">> or Key 'obj_type' is required`,
      `Link failed: user not in the same company, what and specified folder.`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `Couldnt link 1 to alias`,
      `Key 'conv_id' is required`,
      `Project or stage mismatch`,
      `project_short_name has not valid type, expected types [binary]`,
      `stage_short_name has not valid type, expected types [binary]`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,1},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Access denied`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
      `Key 'obj_to_id' is required or Value '1' is not valid. Type of value is not 'binary'`,
      `key: data.entity_type_name.Value '1' is not valid. Type of value is not 'binary'`,
      `Key 'start' is required`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"week\\\">>,<<\\\"month\\\">>]\">>`,
      `group does not coincide with one of the expected values [all, time]`,
      `interval does not coincide with one of the expected values [hour, minute, day]`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"file\\\">>,<<\\\"server\\\">>]\">>`,
      `{\"description\":\"Value '<<\\\"1\\\">>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":1}`,
      `{\"description\":\"Value '1' is not valid. Type of value is not 'binary'\",\"key\":\"company_name\",\"value\":1}`,
      `{\"description\":\"Value '1' is not valid. Type of value is not 'binary'\",\"key\":\"cluster_id\",\"value\":1}`,
      `{\"description\":\"Value '1' is not valid. Type of value is not 'binary'\",\"key\":\"name\",\"value\":1}`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"without_nodes\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>,<<\\\"escalation\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
      `Stage of object do not matches stageId of request`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>]\">> or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"active\\\">>,<<\\\"paused\\\">>,<<\\\"debug\\\">>,<<\\\"active_debug\\\">>]\">>`,
      `Key '1' not found in config`,
      `Value '1' is not valid for type 'boolean'`,
      `Value 'null' is not valid for type 'boolean'`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"binary\\\">>,<<\\\"integer\\\">>,<<\\\"positive_integer\\\">>,<<\\\"atom\\\">>,<<\\\"json\\\">>,\\n <<\\\"float\\\">>,<<\\\"string\\\">>,<<\\\"boolean\\\">>]\">>`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
      `Value '<<\"new_user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '1' is not valid. Type of value is not 'binary' or Value '<<\"new_user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Bad object 'values'`,
      `Bad object '1'`,
      `Key 'scopes_filter' is invalid, allowed only *, api_call, set_param, go_if_const, api_copy, db_call, api_rpc, api_rpc_reply, api_code`,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"explain\\\">>,<<\\\"completion\\\">>]\">>`,
      `invalid extra types`,
      `invalid value result data types`,
    ],
  },
  {
    input: null,
    errors: [
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value 'null' is not valid. Type of value is not 'binary' or Key 'sort' is required`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value is not valid`,
      `obj_type does not coincide with one of the expected values [user]`,
      `type has not valid type, expected types [binary]`,
      `Value is not valid email`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"admins\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"actived\\\">>,<<\\\"not_actived\\\">>,<<\\\"deleted\\\">>]\">>`,
      `Params are not valid`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"basic\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function_build\\\">>]\">>`,
      `privs are not valid`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `pattern has not valid type, expected types [binary]`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ssh_key\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function\\\">>,<<\\\"function_build\\\">>,<<\\\"ssh_key\\\">>]\">>`,
      `from_node_id has not valid type, expected types [binary]`,
      `to_node_id has not valid type, expected types [binary]`,
      `obj_id has not valid type, expected types [binary]`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `token has not valid type, expected types [binary]`,
      `callback_hash has not valid type, expected types [binary]`,
      `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      `obj_type has not valid type, expected types [binary]`,
      `result has not valid type, expected types [binary]`,
      `description has not valid type, expected types [binary]`,
      `Wrong object reference. Validation error`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `invalid timer value`,
      `to small timer value, min must be 10`,
      `Unexpected logic type`,
      `invalid conditions`,
      `invalid ConvId`,
      `invalid mode`,
      `check result data types`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `One of keys obj_id or obj_short_name is required`,
      `task not found`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required or Key 'folder_id' is required`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'obj_id' is required`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"uncommited\\\">>]\">>`,
      `Value 'null' is not valid project_id or Key 'project_short_name' is required`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Couldnt link null to alias or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Key 'obj_to' is required or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">> or Key 'obj_type' is required`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `Couldnt link null to alias`,
      `Key 'conv_id' is required`,
      `Project or stage mismatch`,
      `project_short_name has not valid type, expected types [binary]`,
      `stage_short_name has not valid type, expected types [binary]`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,null},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
      `Key 'obj_to_id' is required or Value 'null' is not valid. Type of value is not 'binary'`,
      `key: data.entity_type_name.Value 'null' is not valid. Type of value is not 'binary'`,
      `Key 'start' is required`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"week\\\">>,<<\\\"month\\\">>]\">>`,
      `group does not coincide with one of the expected values [all, time]`,
      `interval does not coincide with one of the expected values [hour, minute, day]`,
      `object does not exists`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"file\\\">>,<<\\\"server\\\">>]\">>`,
      `{\"description\":\"Value '<<\\\"null\\\">>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":null}`,
      `{\"description\":\"Value 'null' is not valid. Type of value is not 'binary'\",\"key\":\"company_name\",\"value\":null}`,
      `{\"description\":\"Value 'null' is not valid. Type of value is not 'binary'\",\"key\":\"cluster_id\",\"value\":null}`,
      `{\"description\":\"Value 'null' is not valid. Type of value is not 'binary'\",\"key\":\"name\",\"value\":null}`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"without_nodes\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>,<<\\\"escalation\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
      `Project of object do not matches projectId of request`,
      `Stage of object do not matches stageId of request`,
      `Key 'null' not found in config`,
      `Value 'null' is not valid for type 'boolean'`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"binary\\\">>,<<\\\"integer\\\">>,<<\\\"positive_integer\\\">>,<<\\\"atom\\\">>,<<\\\"json\\\">>,\\n <<\\\"float\\\">>,<<\\\"string\\\">>,<<\\\"boolean\\\">>]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
      `Bad object 'values'`,
      `Bad object 'null'`,
      `Object's company ID does not match company ID in the request`,
      `Key 'scopes_filter' is invalid, allowed only *, api_call, set_param, go_if_const, api_copy, db_call, api_rpc, api_rpc_reply, api_code`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"explain\\\">>,<<\\\"completion\\\">>]\">>`,
      `timezone_offset has not valid type, expected types [integer, binary_integer]`,
      `invalid extra types`,
      `invalid value result data types`,
    ],
  },
];

export const stringNotValidTestCases = [
  {
    input: 'test',
    errors: [
      `Value is not valid`,
      `Value is not valid uuid-v4 value or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24`,
      `Value 'test' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `Value is not valid`,
      `sql: ping: dial tcp: lookup test on ***********:53: no such host`,
      `sql: ping: pq: SSL is not enabled on the server`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"api_key\\\">>,<<\\\"group\\\">>,<<\\\"shared\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `obj_type does not coincide with one of the expected values [user]`,
      `Value is not valid email`,
      `Company test does not exists`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"google\\\">>,<<\\\"corezoid\\\">>]\">>`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"admins\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"actived\\\">>,<<\\\"not_actived\\\">>,<<\\\"deleted\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"function_build\\\">>]\">>`,
      `privs are not valid`,
      `from_node_id has not valid type, expected types [binary]`,
      `obj_id is not found`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `Viber responce: invalidAuthToken`,
      `callback_hash less than minimum allowed size (range) 40`,
      `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      `obj_type does not coincide with one of the expected values [slack, telegram, skype, fbmessenger]`,
      `result does not coincide with one of the expected values [error]`,
      `Wrong object reference. Validation error`,
      `Wrong object linked to alias`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `invalid timer value`,
      `to small timer value, min must be 10`,
      `Unexpected logic type`,
      `invalid conditions`,
      `invalid ConvId`,
      `invalid mode`,
      `check result data types`,
      `Object alias not found`,
      `Stage not found`,
      `Project is not found`,
      `task_not_found`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ref\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"eq\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `object does not exists`,
      `Key 'obj_short_name' is required or Value is not valid`,
      `Value 'test' is not valid project_id or Key 'project_short_name' is required`,
      `Key 'project_short_name' is required or Value 'test' is not valid project_id`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"uncommited\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Key 'obj_to' is required or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">> or Key 'obj_type' is required`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `Couldnt link test to alias`,
      `Key 'conv_id' is required`,
      `project_short_name has not valid type, expected types [binary]`,
      `invalid email`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 40`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"test\\\">>},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"company\\\">>,<<\\\"conv\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"api\\\">>,<<>>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"traff\\\">>,<<\\\"tacts\\\">>,<<\\\"opers\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"month\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
      `user wasn't found for these login, login_type`,
      `Key 'obj_to_id' is required or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ldap\\\">>,<<\\\"google\\\">>,<<\\\"facebook\\\">>,<<\\\"api\\\">>,<<\\\"phone\\\">>,<<\\\"corezoid\\\">>,\\n <<\\\"github\\\">>]\">>`,
      `Key 'start' is required`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"week\\\">>,<<\\\"month\\\">>]\">>`,
      `Key stage_to_id. Value is not valid`,
      `group does not coincide with one of the expected values [all, time]`,
      `interval does not coincide with one of the expected values [hour, minute, day]`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ui\\\">>,<<\\\"timer\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"upload\\\">>,<<\\\"download\\\">>,<<\\\"copy\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"obj_scheme\\\">>,<<\\\"csv_upload\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"task\\\">>,<<\\\"obj_scheme\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"file\\\">>,<<\\\"server\\\">>]\">>`,
      `{\"description\":\"Value '<<\\\"test\\\">>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":\"test\"}`,
      `{\"description\":\"Attr test not found\",\"key\":\"name\",\"value\":\"test\"}`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"active\\\">>,<<\\\"paused\\\">>,<<\\\"debug\\\">>,<<\\\"blocked\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"without_nodes\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>,<<\\\"escalation\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
      `Key 'test' not found in config`,
      `Couldn't update value 'true'. Key: 'test', type: 'boolean'`,
      `Value 'test' is not valid for type 'boolean'`,
      `Value 'null' is not valid for type 'boolean'`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"binary\\\">>,<<\\\"integer\\\">>,<<\\\"positive_integer\\\">>,<<\\\"atom\\\">>,<<\\\"json\\\">>,\\n <<\\\"float\\\">>,<<\\\"string\\\">>,<<\\\"boolean\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"user_permissions\\\">>]\">>`,
      `Bad object 'values'`,
      `Bad object 'test'`,
      `Object's company ID does not match company ID in the request`,
      `Key 'scopes_filter' is invalid, allowed only *, api_call, set_param, go_if_const, api_copy, db_call, api_rpc, api_rpc_reply, api_code`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"sandbox\\\">>,<<\\\"production\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"erl\\\">>,<<\\\"js\\\">>]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"explain\\\">>,<<\\\"completion\\\">>]\">>`,
      `invalid value result data types`,
    ],
  },
  {
    input: '',
    errors: [
      `Value is not valid`,
      `Value is not valid uuid-v4 value or Value '<<>>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `Value is not valid`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 1`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"api_key\\\">>,<<\\\"group\\\">>,<<\\\"shared\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `obj_type does not coincide with one of the expected values [user]`,
      `Value is not valid email`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"google\\\">>,<<\\\"corezoid\\\">>]\">>`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"admins\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"actived\\\">>,<<\\\"not_actived\\\">>,<<\\\"deleted\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"function_build\\\">>]\">>`,
      `privs are not valid`,
      `from_node_id has not valid type, expected types [binary]`,
      `obj_id is not found`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `token less than minimum allowed size (range) 1`,
      `callback_hash less than minimum allowed size (range) 40`,
      `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      `obj_type does not coincide with one of the expected values [slack, telegram, skype, fbmessenger]`,
      `result does not coincide with one of the expected values [error]`,
      `Wrong object reference. Validation error`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
      `invalid timer value`,
      `to small timer value, min must be 10`,
      `Unexpected logic type`,
      `invalid conditions`,
      `invalid ConvId`,
      `invalid mode`,
      `check result data types`,
      `Object alias not found`,
      `task_not_found`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"ref\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"eq\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'obj_id' is required`,
      `Key 'obj_short_name' is required or Value is not valid`,
      `Value '' is not valid project_id or Key 'project_short_name' is required`,
      `Key 'project_short_name' is required or Value '' is not valid project_id`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"uncommited\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Key 'obj_to' is required or Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">> or Key 'obj_type' is required`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `Couldnt link  to alias`,
      `Key 'conv_id' is required`,
      `Project or stage mismatch`,
      `project_short_name has not valid type, expected types [binary]`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 3`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 40`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<>>},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"company\\\">>,<<\\\"conv\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"traff\\\">>,<<\\\"tacts\\\">>,<<\\\"opers\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"month\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
      `user wasn't found for these login, login_type`,
      `Key 'obj_to_id' is required or Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"ldap\\\">>,<<\\\"google\\\">>,<<\\\"facebook\\\">>,<<\\\"api\\\">>,<<\\\"phone\\\">>,<<\\\"corezoid\\\">>,\\n <<\\\"github\\\">>]\">>`,
      `Key 'start' is required`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"week\\\">>,<<\\\"month\\\">>]\">>`,
      `Key stage_to_id. Value is not valid`,
      `group does not coincide with one of the expected values [all, time]`,
      `interval does not coincide with one of the expected values [hour, minute, day]`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"upload\\\">>,<<\\\"download\\\">>,<<\\\"copy\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"obj_scheme\\\">>,<<\\\"csv_upload\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"task\\\">>,<<\\\"obj_scheme\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"file\\\">>,<<\\\"server\\\">>]\">>`,
      `{\"description\":\"Value '<<>>' is not valid. Value is not in allowed list <<\\\"[<<\\\\\\\"binary\\\\\\\">>,<<\\\\\\\"base64\\\\\\\">>]\\\">>\",\"key\":\"format\",\"value\":\"\"}`,
      `{\"description\":\"Value is not valid. Value's byte_size is less than minimum allowed: 1\",\"key\":\"name\",\"value\":\"\"}`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"active\\\">>,<<\\\"paused\\\">>,<<\\\"debug\\\">>,<<\\\"blocked\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"without_nodes\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>,<<\\\"escalation\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
      `Project of object do not matches projectId of request`,
      `Stage of object do not matches stageId of request`,
      `Key '' not found in config`,
      `Couldn't update value 'true'. Key: '', type: 'boolean'`,
      `Value '' is not valid for type 'boolean'`,
      `Value 'null' is not valid for type 'boolean'`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"binary\\\">>,<<\\\"integer\\\">>,<<\\\"positive_integer\\\">>,<<\\\"atom\\\">>,<<\\\"json\\\">>,\\n <<\\\"float\\\">>,<<\\\"string\\\">>,<<\\\"boolean\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"user_permissions\\\">>]\">>`,
      `Bad object 'values'`,
      `Bad object ''`,
      `Object's company ID does not match company ID in the request`,
      `Key 'scopes_filter' is empty`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"sandbox\\\">>,<<\\\"production\\\">>]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"erl\\\">>,<<\\\"js\\\">>]\">>`,
      `error, copilot_sdk`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"explain\\\">>,<<\\\"completion\\\">>]\">>`,
      `invalid value result data types`,
    ],
  },
];

export const boolTestCases = [
  {
    input: {},
    errors: [
      `Value is not valid`,
      `Value '{[]}' is not valid. Type of value is not 'binary' or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '{[]}' is not valid. Type of value is not 'binary'`,
      `Value '{[]}' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '{[]}' is not valid. Type of value is not 'boolean'`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Key async. Value is not valid`,
      `Key names_in_first_row. Value is not valid`,
      `Key extra_sys_ip. Value is not valid`,
      `Key extra_sys_filename. Value is not valid`,
      `timestamp has not valid type, expected types [boolean]`,
      `invalid value response or response_type, or both`,
      `{\"description\":\"Value '{[]}' is not valid. Type of value is not 'boolean'\",\"key\":\"multi_tenancy\",\"value\":{}}`,
      `{\"description\":\"Value {[]} not allowed if attr type is boolean\",\"key\":\"value\",\"value\":{}}`,
    ],
  },
  {
    input: [],
    errors: [
      `Value is not valid`,
      `Value '[]' is not valid. Type of value is not 'binary' or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '[]' is not valid. Type of value is not 'binary'`,
      `Value '[]' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '[]' is not valid. Type of value is not 'boolean'`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Key async. Value is not valid`,
      `Key names_in_first_row. Value is not valid`,
      `Key extra_sys_ip. Value is not valid`,
      `Key extra_sys_filename. Value is not valid`,
      `timestamp has not valid type, expected types [boolean]`,
      `invalid value response or response_type, or both`,
      `{\"description\":\"Value '[]' is not valid. Type of value is not 'boolean'\",\"key\":\"multi_tenancy\",\"value\":[]}`,
      `{\"description\":\"Value  not allowed if attr type is boolean\",\"key\":\"value\",\"value\":[]}`,
    ],
  },
  {
    input: 0,
    errors: [
      `Object dashboard with id 0 does not exist`,
      `Value '0' is not valid. Type of value is not 'binary' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '0' is not valid. Type of value is not 'binary'`,
      `This version hasn't any commit`,
      `Object conv with id 0 does not exist`,
      `Value is not valid`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '0' is not valid. Type of value is not 'boolean'`,
      `Object user with id 0 does not exist`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Key async. Value is not valid`,
      `Key names_in_first_row. Value is not valid`,
      `Key extra_sys_ip. Value is not valid`,
      `Key extra_sys_filename. Value is not valid`,
      `timestamp has not valid type, expected types [boolean]`,
      `invalid value response or response_type, or both`,
      `{\"description\":\"Value '0' is not valid. Type of value is not 'boolean'\",\"key\":\"multi_tenancy\",\"value\":0}`,
      `{\"description\":\"Value 0 not allowed if attr type is boolean\",\"key\":\"value\",\"value\":0}`,
    ],
  },
  {
    input: -1,
    errors: [
      `Value is not valid. Value's limit is less than minimum allowed: 0`,
      `Value '-1' is not valid. Type of value is not 'binary' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '-1' is not valid. Type of value is not 'binary'`,
      `Object dashboard with id -1 does not exist`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid. Value's limit is less than minimum allowed: 0`,
      `This version hasn't any commit`,
      `Value is not valid`,
      `Value '-1' is not valid. Type of value is not 'boolean'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid. Value's limit is less than minimum allowed: 0`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Key async. Value is not valid`,
      `Key names_in_first_row. Value is not valid`,
      `Key extra_sys_ip. Value is not valid`,
      `Key extra_sys_filename. Value is not valid`,
      `timestamp has not valid type, expected types [boolean]`,
      `invalid value response or response_type, or both`,
      `{\"description\":\"Value '-1' is not valid. Type of value is not 'boolean'\",\"key\":\"multi_tenancy\",\"value\":-1}`,
      `{\"description\":\"Value -1 not allowed if attr type is boolean\",\"key\":\"value\",\"value\":-1}`,
    ],
  },
  {
    input: 1,
    errors: [
      `Value is not valid. Value's limit is less than minimum allowed: 0`,
      `Value '-1' is not valid. Type of value is not 'binary' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value '-1' is not valid. Type of value is not 'binary'`,
      `Object dashboard with id -1 does not exist`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid. Value's limit is less than minimum allowed: 0`,
      `This version hasn't any commit`,
      `Value is not valid`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '1' is not valid. Type of value is not 'boolean'`,
      `Link failed: user is not in the same company, what and specified dashboard.`,
      `token has not valid type, expected types [binary]`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Value '1' is not valid. Type of value is not 'binary'`,
      `Key async. Value is not valid`,
      `Key names_in_first_row. Value is not valid`,
      `Key extra_sys_ip. Value is not valid`,
      `Key extra_sys_filename. Value is not valid`,
      `timestamp has not valid type, expected types [boolean]`,
      `invalid value response or response_type, or both`,
      `{\"description\":\"Value '1' is not valid. Type of value is not 'boolean'\",\"key\":\"multi_tenancy\",\"value\":1}`,
      `{\"description\":\"Value 1 not allowed if attr type is boolean\",\"key\":\"value\",\"value\":1}`,
    ],
  },
  {
    input: null,
    errors: [
      `Value is not valid`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value 'null' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `Value '' is not valid. Type of value is not 'boolean'`,
      `Value 'null' is not valid. Type of value is not 'boolean'`,
      `token has not valid type, expected types [binary]`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Key async. Value is not valid`,
      `Key names_in_first_row. Value is not valid`,
      `Key extra_sys_ip. Value is not valid`,
      `Key extra_sys_filename. Value is not valid`,
      `timestamp has not valid type, expected types [boolean]`,
      `invalid value response or response_type, or both`,
      `{\"description\":\"Value 'null' is not valid. Type of value is not 'boolean'\",\"key\":\"multi_tenancy\",\"value\":null}`,
      `{\"description\":\"Value null not allowed if attr type is boolean\",\"key\":\"value\",\"value\":null}`,
    ],
  },
  {
    input: 'test',
    errors: [
      `Value is not valid`,
      `Value is not valid uuid-v4 value or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24`,
      `Value 'test' is not valid. Type of value is not 'integer'`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value 'test' is not valid. Type of value is not 'boolean'`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Stage not found`,
      `Key async. Value is not valid`,
      `Key names_in_first_row. Value is not valid`,
      `Key extra_sys_ip. Value is not valid`,
      `Key extra_sys_filename. Value is not valid`,
      `timestamp has not valid type, expected types [boolean]`,
      `invalid value response or response_type, or both`,
      `{\"description\":\"Value 'test' is not valid. Type of value is not 'boolean'\",\"key\":\"multi_tenancy\",\"value\":\"test\"}`,
      `{\"description\":\"Value test not allowed if attr type is boolean\",\"key\":\"value\",\"value\":\"test\"}`,
      `Key 'test' not found in config`,
    ],
  },
  {
    input: '',
    errors: [
      `Value is not valid`,
      `Value is not valid uuid-v4 value or Value '<<>>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `One of keys obj_id or obj_short_name is required or Value is not valid`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value '' is not valid. Type of value is not 'boolean'`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Key async. Value is not valid`,
      `Key names_in_first_row. Value is not valid`,
      `Key extra_sys_ip. Value is not valid`,
      `Key extra_sys_filename. Value is not valid`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 3`,
      `timestamp has not valid type, expected types [boolean]`,
      `invalid value response or response_type, or both`,
      `{\"description\":\"Value '' is not valid. Type of value is not 'boolean'\",\"key\":\"multi_tenancy\",\"value\":\"\"}`,
      `{\"description\":\"Value  not allowed if attr type is boolean\",\"key\":\"value\",\"value\":\"\"}`,
      `Key '' not found in config`,
    ],
  },
];

export const arrayTestCases = [
  {
    input: {},
    errors: [
      `Value '{[]}' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value '{[]}' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value '{[]}' is not valid. Type of value is not 'list' or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value '{[]}' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
      `Value '{[]}' is not valid. Type of value is not 'uniq_list' or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>},{<<\\\"list_obj\\\">>,{[]}}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `node_ids has not valid type, expected types [list]`,
      `{\"description\":\"Value '{[]}' is not valid. Type of value is not 'list'\",\"key\":\"extra_attributes\",\"value\":{}}`,
      `Invalid logics sequence`,
      `invalid value extra`,
      `Not valid logics`,
    ],
  },
  {
    input: true,
    errors: [
      `Value 'true' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value 'true' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value 'true' is not valid. Type of value is not 'list' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value 'true' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
      `Value 'true' is not valid. Type of value is not 'uniq_list' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>},{<<\\\"list_obj\\\">>,true}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `node_ids has not valid type, expected types [list]`,
      `{\"description\":\"Value 'true' is not valid. Type of value is not 'list'\",\"key\":\"extra_attributes\",\"value\":true}`,
      `Invalid logics sequence`,
      `invalid value extra`,
      `Not valid logics`,
    ],
  },
  {
    input: 0,
    errors: [
      `Value '0' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value '0' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value '0' is not valid. Type of value is not 'list' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value '0' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
      `Value '0' is not valid. Type of value is not 'uniq_list' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>},{<<\\\"list_obj\\\">>,0}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `node_ids has not valid type, expected types [list]`,
      `{\"description\":\"Value '0' is not valid. Type of value is not 'list'\",\"key\":\"extra_attributes\",\"value\":0}`,
      `Invalid logics sequence`,
      `invalid value extra`,
      `Not valid logics`,
    ],
  },
  {
    input: -1,
    errors: [
      `Value '-1' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value '-1' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value '-1' is not valid. Type of value is not 'list' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value '-1' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
      `Value '-1' is not valid. Type of value is not 'uniq_list' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>},{<<\\\"list_obj\\\">>,-1}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `node_ids has not valid type, expected types [list]`,
      `{\"description\":\"Value '-1' is not valid. Type of value is not 'list'\",\"key\":\"extra_attributes\",\"value\":-1}`,
      `Value is not valid. Value's limit is less than minimum allowed: 0`,
      `Invalid logics sequence`,
      `invalid value extra`,
      `Not valid logics`,
    ],
  },
  {
    input: 123,
    errors: [
      `Value '123' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value '123' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value '123' is not valid. Type of value is not 'list' or Value '<<\"123\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value '123' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
      `Value '123' is not valid. Type of value is not 'uniq_list' or Value '<<\"123\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"123\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>},{<<\\\"list_obj\\\">>,123}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `node_ids has not valid type, expected types [list]`,
      `{\"description\":\"Value '123' is not valid. Type of value is not 'list'\",\"key\":\"extra_attributes\",\"value\":123}`,
      `Invalid logics sequence`,
      `invalid value extra`,
      `Not valid logics`,
    ],
  },
  {
    input: null,
    errors: [
      `Value 'null' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value 'null' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value '123' is not valid. Type of value is not 'list' or Value '<<\"123\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value 'null' is not valid. Type of value is not 'list' or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value 'null' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
      `Value 'null' is not valid. Type of value is not 'uniq_list' or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>},{<<\\\"list_obj\\\">>,null}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `node_ids has not valid type, expected types [list]`,
      `{\"description\":\"Value 'null' is not valid. Type of value is not 'list'\",\"key\":\"extra_attributes\",\"value\":null}`,
      `Invalid logics sequence`,
      `invalid value extra`,
      `Not valid logics`,
    ],
  },
  {
    input: 'test',
    errors: [
      `Value 'test' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value 'test' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value 'test' is not valid. Type of value is not 'list' or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Viber responce: invalidAuthToken`,
      `Value 'test' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
      `Value 'test' is not valid. Type of value is not 'uniq_list' or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>},{<<\\\"list_obj\\\">>,<<\\\"test\\\">>}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `node_ids has not valid type, expected types [list]`,
      `{\"description\":\"Value 'test' is not valid. Type of value is not 'list'\",\"key\":\"extra_attributes\",\"value\":\"test\"}`,
      `Invalid logics sequence`,
      `invalid value extra`,
      `Not valid logics`,
    ],
  },
  {
    input: '',
    errors: [
      `Value '' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value '' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value '' is not valid. Type of value is not 'list' or Value '<<>>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value '' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `token less than minimum allowed size (range) 1`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Value is not valid`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
      `Value '' is not valid. Type of value is not 'uniq_list' or Value '<<>>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>},{<<\\\"list_obj\\\">>,<<>>}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `node_ids has not valid type, expected types [list]`,
      `{\"description\":\"Value '' is not valid. Type of value is not 'list'\",\"key\":\"extra_attributes\",\"value\":\"\"}`,
      `Invalid logics sequence`,
      `invalid value extra`,
      `Not valid logics`,
    ],
  },
];

export const objectTestCases = [
  {
    input: true,
    errors: [
      `Value 'true' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value 'true' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value is not valid`,
      `Invalid value of the 'data' key`,
      `data has not valid type, expected types [list]`,
      `Value 'true' is not valid. Type of value is not 'list' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[{[]}]\">>`,
      `key: data.data.Invalid value of the 'data' key`,
      `check result data types`,
      `invalid value extra types`,
      `invalid value res_data or res_data_type, or both`,
      `invalid value response or response_type, or both`,
    ],
  },
  {
    input: 0,
    errors: [
      `Value '0' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value '0' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value is not valid`,
      `Invalid value of the 'data' key`,
      `data has not valid type, expected types [list]`,
      `Value '0' is not valid. Type of value is not 'list' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[{[]}]\">>`,
      `key: data.data.Invalid value of the 'data' key`,
      `check result data types`,
      `invalid value extra types`,
      `invalid value res_data or res_data_type, or both`,
      `invalid value response or response_type, or both`,
    ],
  },
  {
    input: -1,
    errors: [
      `Value '-1' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value '-1' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value is not valid`,
      `Invalid value of the 'data' key`,
      `data has not valid type, expected types [list]`,
      `Value '-1' is not valid. Type of value is not 'list' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[{[]}]\">>`,
      `key: data.data.Invalid value of the 'data' key`,
      `check result data types`,
      `invalid value extra types`,
      `invalid value res_data or res_data_type, or both`,
      `invalid value response or response_type, or both`,
    ],
  },
  {
    input: 123,
    errors: [
      `Value '123' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value '123' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value is not valid`,
      `Invalid value of the 'data' key`,
      `data has not valid type, expected types [list]`,
      `Value '123' is not valid. Type of value is not 'list' or Value '<<\"123\">>' is not valid. Value is not in allowed list <<\"[{[]}]\">>`,
      `key: data.data.Invalid value of the 'data' key`,
      `check result data types`,
      `invalid value extra types`,
      `invalid value res_data or res_data_type, or both`,
      `invalid value response or response_type, or both`,
    ],
  },
  {
    input: null,
    errors: [
      `Value 'null' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value 'null' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value is not valid`,
      `Invalid value of the 'data' key`,
      `data has not valid type, expected types [list]`,
      `Value 'null' is not valid. Type of value is not 'list' or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[{[]}]\">>`,
      `key: data.data.Invalid value of the 'data' key`,
      `check result data types`,
      `invalid value extra types`,
      `invalid value res_data or res_data_type, or both`,
      `invalid value response or response_type, or both`,
    ],
  },
  {
    input: 'test',
    errors: [
      `Value 'test' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value 'test' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value is not valid`,
      `Invalid value of the 'data' key`,
      `data has not valid type, expected types [list]`,
      `Value 'test' is not valid. Type of value is not 'list' or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[{[]}]\">>`,
      `key: data.data.Invalid value of the 'data' key`,
      `check result data types`,
      `invalid value extra types`,
      `invalid value res_data or res_data_type, or both`,
      `invalid value response or response_type, or both`,
    ],
  },
  {
    input: '',
    errors: [
      `Value '' is not valid. Type of value is not 'list'`,
      `privs has not valid type, expected types [list_in_list, void_list]`,
      `list_obj does not coincide with one of the expected values [all]`,
      `Value '' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Params are not valid`,
      `Value is not valid`,
      `Invalid value of the 'data' key`,
      `data has not valid type, expected types [list]`,
      `Value '' is not valid. Type of value is not 'list' or Value '<<>>' is not valid. Value is not in allowed list <<\"[{[]}]\">>`,
      `key: data.data.Invalid value of the 'data' key`,
      `check result data types`,
      `invalid value extra types`,
      `invalid value res_data or res_data_type, or both`,
      `invalid value response or response_type, or both`,
    ],
  },
];

export const companyTestCases = [
  {
    input: true,
    errors: [
      `Value is not valid`,
      `Value 'true' is not valid. Type of value is not 'binary'`,
      `company_id does not coincide with one of the expected values [null]`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `Key company_id. Value 'true' is not valid. Type of value is not 'binary'`,
      `invite not found`,
    ],
  },
  {
    input: 0,
    errors: [
      `Value is not valid`,
      `Invalid dbcall instance`,
      `Value '0' is not valid. Type of value is not 'binary'`,
      `company_id does not coincide with one of the expected values [null]`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `Key company_id. Value '0' is not valid. Type of value is not 'binary'`,
      `invite not found`,
    ],
  },
  {
    input: {},
    errors: [
      `Value is not valid`,
      `Value '{[]}' is not valid. Type of value is not 'binary'`,
      `company_id does not coincide with one of the expected values [null]`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `Key company_id. Value '{[]}' is not valid. Type of value is not 'binary'`,
      `invite not found`,
    ],
  },
  {
    input: [],
    errors: [
      `Value is not valid`,
      `Value '[]' is not valid. Type of value is not 'binary'`,
      `company_id does not coincide with one of the expected values [null]`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `Key company_id. Value '[]' is not valid. Type of value is not 'binary'`,
      `invite not found`,
    ],
  },
  {
    input: -1,
    errors: [
      `Value is not valid`,
      `Value '-1' is not valid. Type of value is not 'binary'`,
      `company_id does not coincide with one of the expected values [null]`,
      `Value is not valid. Value's limit is less than minimum allowed: 0`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `Key company_id. Value '-1' is not valid. Type of value is not 'binary'`,
      `invite not found`,
    ],
  },
  {
    input: 1,
    errors: [
      `Value is not valid`,
      `Value '1' is not valid. Type of value is not 'binary'`,
      `company_id does not coincide with one of the expected values [null]`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `Key company_id. Value '1' is not valid. Type of value is not 'binary'`,
      `invite not found`,
    ],
  },
  {
    input: 123,
    errors: [
      `Value is not valid`,
      `Value '123' is not valid. Type of value is not 'binary'`,
      `company_id does not coincide with one of the expected values [null]`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `Key company_id. Value '123' is not valid. Type of value is not 'binary'`,
      `invite not found`,
    ],
  },
  {
    input: 'te',
    errors: [
      `Company te does not exists`,
      `Invalid dbcall instance`,
      `Access denied`,
      `Object's company ID does not match company ID in the request`,
      `source has to be built`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 3`,
      `task not found`,
      `Value is not valid`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `invite not found`,
    ],
  },
  {
    input: faker.random.alphaNumeric(256),
    errors: [
      `Value is not valid`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `invite not found`,
    ],
  },
];

export const undefinedTestCase = [
  {
    input: undefined,
    errors: [
      `Key 'obj_id' is required`,
      `Key 'obj_type' is required or Key 'sort' is required`,
      `Key 'name' is required`,
      `Key 'dashboard_id' is required`,
      `Key 'series' is required`,
      `One of keys obj_id or obj_short_name is required or Key 'conv_id' is required`,
      `Key 'obj_type' is required or Key 'conv_id' is required`,
      `Key 'version' is required`,
      `Key 'instance_type' is required`,
      `Key 'data.ssl' is required`,
      `Key 'data.password' is required`,
      `Key 'data.driver' is required`,
      `Key 'data.host' is required`,
      `Key 'data.username' is required`,
      `Key 'data.port' is required`,
      `Key 'data.database' is required`,
      `Key 'data.timeoutMs' is required`,
      `Key 'favorite' is required`,
      `Key 'list_obj' is required`,
      `obj_id field is missing`,
      `obj_type does not coincide with one of the expected values [user]`,
      `list_obj field is missing`,
      `Key 'site' is required`,
      `Key 'description' is required`,
      `Key 'login' is required`,
      `Key 'login_type' is required`,
      `Key 'auth_providers' is required`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Key 'title' is required`,
      `Key 'obj_type' is required`,
      `Key 'status' is required`,
      `Params are not valid`,
      `Key 'time_range.timezone_offset' is required`,
      `Key 'conv_id' is required`,
      `Key 'data.user' is required`,
      `Key 'data.pass' is required`,
      `Key 'data' is required`,
      `Key 'grid.obj_id' is required or Value '<<\"[[{<<\\\"x\\\">>,0},{<<\\\"y\\\">>,0},{<<\\\"width\\\">>,4},{<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},{<<\\\"y\\\">>,0},{<<\\\"width\\\">>,4},{<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},{<<\\\"x\\\">>,0},{<<\\\"width\\\">>,4},{<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},{<<\\\"x\\\">>,0},{<<\\\"y\\\">>,0},{<<\\\"height\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 24 or Value '<<\"[[{<<\\\"obj_id\\\">>,<<\\\"103065\\\">>},{<<\\\"x\\\">>,0},{<<\\\"y\\\">>,0},{<<\\\"width\\\">>,4}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Key 'node_id' is required`,
      `Key 'lang' is required`,
      `Key 'code' is required`,
      `Key 'commit' is required`,
      `Key 'repo' is required`,
      `Key 'script' is required`,
      `Object's company ID does not match company ID in the request`,
      `privs are not valid`,
      `Key 'obj_to' is required`,
      `timeout`,
      'env field is missing',
      `Key 'src' is required`,
      `conv_id field is missing`,
      `from_node_id field is missing`,
      `to_node_id field is missing`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Key 'obj_to_id' is required`,
      `Unexpected obj_type value. Allowed: telegram | skype | slack | fbmessenger | viber.`,
      `folder_id field is missing`,
      `callback_hash field is missing`,
      `obj_type field is missing`,
      `limit field is missing`,
      `offset field is missing`,
      `result field is missing`,
      `description field is missing`,
      `Key 'messengers' is required`,
      `Wrong object reference. Validation error`,
      `There is unconfirmed previous version`,
      `Key 'limit' is required`,
      `Key 'logics' is required`,
      `invalid timer value`,
      `missed fields`,
      `Key 'end' is required`,
      `Unexpected logic type`,
      `invalid conditions`,
      `invalid Extra`,
      `missed parametrs`,
      `invalid response and response_types length`,
      `Key 'pattern' is required`,
      `Key 'company_id' is required`,
      `Key 'short_name' is required`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `One of keys obj_id or obj_short_name is required`,
      `task not found`,
      `Key 'obj_to_id' is required`,
      `task_not_found`,
      `data field is missing`,
      `Key 'filters.name' is required`,
      `Key 'filters.fun' is required`,
      `Key 'filters.value' is required`,
      `Key 'project_short_name' is required or Key 'project_id' is required or Key 'folder_id' is required`,
      `Key 'obj_short_name' is required or Key 'obj_id' is required`,
      `Key 'project_short_name' is required or Key 'project_id' is required`,
      `One of keys 'project_short_name', 'project_id' is required or Key 'obj_id' is required`,
      `Key 'obj_to_id' is required or Value '<<\"conv\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Key 'obj_to_type' is required or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `Key 'obj_to' is required or Key 'folder_id' is required`,
      `Key 'obj_to' is required or Key 'obj_type' is required`,
      `Key 'link' is required`,
      `Key 'obj_to_type' is required`,
      `Project or stage mismatch`,
      `Key 'version_id' is required`,
      `Key 'project_id' is required`,
      `Key 'link_to_obj' is required`,
      `Key 'hash' is required`,
      `Key 'privs.list_obj' is required or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"create\\\">>}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Key 'privs.type' is required or Value '<<\"[[{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `Couldn't convert value '' for key 'conv_id' `,
      `Key async. Value is not valid`,
      `Key names_in_first_row. Value is not valid`,
      `Key extra_sys_ip. Value is not valid`,
      `Key extra_sys_filename. Value is not valid`,
      `Key 'start' is required`,
      `Key 'filter' is required`,
      `Key 'interval' is required`,
      `Key 'obj_to_id' is required or Key 'obj_to_login' is required`,
      `Key 'order_field' is required`,
      `Key 'order_by' is required`,
      `Key 'obj_to_id' is required or Key 'obj_to_type' is required`,
      `Key 'data.entity_id' is required`,
      `Key 'data.entity_type_name' is required`,
      `Key 'data.data' is required`,
      `missed field Extra`,
      `Key 'res_data' is required`,
      `start field is missing`,
      `end field is missing`,
      `No dashboard_id neither conv_id not set`,
      `group field is missing`,
      `interval field is missing`,
      `Key 'company_name' is required`,
      `Key 'cluster_id' is required`,
      `Key 'start_time' is required`,
      `Key 'expire_time' is required`,
      `Key 'value' is required`,
      `Key 'key' is required`,
      `Value 'null' is not valid for type 'boolean'`,
      `Bad object 'values'`,
      `Bad object 'undefined'`,
      `Key 'env' is required`,
      `Key 'method' is required`,
      `Logic type not specified.`,
      `invalid value extra`,
      `invalid extra types`,
      `Value is not valid`,
      `invalid value res_data`,
      `invalid value res_data or res_data_type, or both`,
      `invalid value response or response_type, or both`,
    ],
  },
];

export const securityTestCases = [
  {
    input: `SELECT * FROM users WHERE id = '' OR 1=1 --'`,
    errors: [
      `Value is not valid`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"table\\\">>,<<\\\"funnel\\\">>,<<\\\"column\\\">>,<<\\\"pie\\\">>]\">> or Key 'sort' is required`,
      `Value 'SELECT * FROM users WHERE id = '' OR 1=1 --'' is not valid. Type of value is not 'list'`,
      `Company SELECT * FROM users WHERE id = '' OR 1=1 --' does not exists`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `sql: ping: parse \"******************************* * FROM users WHERE id = '' OR 1=1 --':5432/dbcall?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `sql: ping: parse \"postgres://SELECT * FROM users WHERE id = '' OR 1=1 --':<EMAIL>:5432/dbcall?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `sql: ping: parse \"postgres://postgres:SELECT * FROM users WHERE id = '' OR 1=1 --'@COR-6938.middleware.loc:5432/dbcall?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `sql: ping: pq: SSL is not enabled on the server`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>]\">>`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"api_key\\\">>,<<\\\"group\\\">>,<<\\\"shared\\\">>]\">>`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `Invalid dbcall instance`,
      `Value is not valid email`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"google\\\">>,<<\\\"corezoid\\\">>]\">>`,
      `Value 'SELECT * FROM users WHERE id = '' OR 1=1 --'' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Access denied`,
      `Params are not valid`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value 'SELECT * FROM users WHERE id = '' OR 1=1 --'' is not valid. Type of value is not 'list' or Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
      `Value 'SELECT * FROM users WHERE id = '' OR 1=1 --'' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Value 'SELECT * FROM users WHERE id = '' OR 1=1 --'' is not valid project_id or Key 'project_short_name' is required`,
      `Value 'SELECT * FROM users WHERE id = '' OR 1=1 --'' is not valid stage_id or Key 'stage_short_name' is required`,
      `obj_id is not found`,
      `Viber responce: invalidAuthToken`,
      `result does not coincide with one of the expected values [error]`,
      `invalid mode`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `limit has not valid type, expected types [integer]`,
      `invalid email`,
      `Value '<<\"SELECT * FROM users WHERE id = '' OR 1=1 --'\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `sql: ping: parse \"postgres://SELECT * FROM users WHERE id = '' OR 1=1 --':3sha1cheeH4uoziej1u@**********:5432/test?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `sql: ping: parse \"*********************************************** * FROM users WHERE id = '' OR 1=1 --':5432/test?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `sql: ping: parse \"postgres://test_user:SELECT * FROM users WHERE id = '' OR 1=1 --'@**********:5432/test?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `Value 'SELECT * FROM users WHERE id = '' OR 1=1 --'' is not valid. Type of value is not 'integer'`,
    ],
  },
  {
    input: `SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;`,
    errors: [
      `Value is not valid`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"table\\\">>,<<\\\"funnel\\\">>,<<\\\"column\\\">>,<<\\\"pie\\\">>]\">> or Key 'sort' is required`,
      `Value 'SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;' is not valid. Type of value is not 'list'`,
      `Company SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin; does not exists`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `sql: ping: parse \"******************************* username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;:5432/dbcall?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `sql: ping: parse \"postgres://SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;:<EMAIL>:5432/dbcall?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `sql: ping: parse \"postgres://postgres:SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;@COR-6938.middleware.loc:5432/dbcall?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `sql: ping: pq: SSL is not enabled on the server`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>]\">>`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"api_key\\\">>,<<\\\"group\\\">>,<<\\\"shared\\\">>]\">>`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `Invalid dbcall instance`,
      `Value is not valid email`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"google\\\">>,<<\\\"corezoid\\\">>]\">>`,
      `Value 'SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Access denied`,
      `Params are not valid`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value 'SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;' is not valid. Type of value is not 'list' or Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
      `Value 'SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Value 'SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;' is not valid project_id or Key 'project_short_name' is required`,
      `Value 'SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;' is not valid stage_id or Key 'stage_short_name' is required`,
      `obj_id is not found`,
      `Viber responce: invalidAuthToken`,
      `result does not coincide with one of the expected values [error]`,
      `invalid mode`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `limit has not valid type, expected types [integer]`,
      `invalid email`,
      `Value '<<\"SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `sql: ping: parse \"*********************************************** username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;:5432/test?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `sql: ping: parse \"postgres://test_user:SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;@**********:5432/test?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `sql: ping: parse \"postgres://SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;:3sha1cheeH4uoziej1u@**********:5432/test?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `Value 'SELECT username, email FROM users WHERE id = '1' UNION SELECT password, '' FROM admin;' is not valid. Type of value is not 'integer'`,
    ],
  },
  {
    input: `SELECT username FROM users WHERE id = '1' AND 1=1;`,
    errors: [
      `Value is not valid`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"table\\\">>,<<\\\"funnel\\\">>,<<\\\"column\\\">>,<<\\\"pie\\\">>]\">> or Key 'sort' is required`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1=1;' is not valid. Type of value is not 'list'`,
      `Company SELECT username FROM users WHERE id = '1' AND 1=1; does not exists`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `sql: ping: parse \"******************************* username FROM users WHERE id = '1' AND 1=1;:5432/dbcall?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `sql: ping: parse \"postgres://SELECT username FROM users WHERE id = '1' AND 1=1;:<EMAIL>:5432/dbcall?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `sql: ping: parse \"postgres://postgres:SELECT username FROM users WHERE id = '1' AND 1=1;@COR-6938.middleware.loc:5432/dbcall?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `sql: ping: pq: SSL is not enabled on the server`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"api_key\\\">>,<<\\\"group\\\">>,<<\\\"shared\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `Invalid dbcall instance`,
      `Value is not valid email`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"google\\\">>,<<\\\"corezoid\\\">>]\">>`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1=1;' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Access denied`,
      `Params are not valid`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1=1;' is not valid. Type of value is not 'list' or Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1=1;' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1=1;' is not valid project_id or Key 'project_short_name' is required`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1=1;' is not valid stage_id or Key 'stage_short_name' is required`,
      `obj_id is not found`,
      `Viber responce: invalidAuthToken`,
      `result does not coincide with one of the expected values [error]`,
      `invalid mode`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `limit has not valid type, expected types [integer]`,
      `invalid email`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1=1;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `sql: ping: parse \"*********************************************** username FROM users WHERE id = '1' AND 1=1;:5432/test?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `sql: ping: parse \"postgres://test_user:SELECT username FROM users WHERE id = '1' AND 1=1;@**********:5432/test?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `sql: ping: parse \"postgres://SELECT username FROM users WHERE id = '1' AND 1=1;:3sha1cheeH4uoziej1u@**********:5432/test?sslmode=disable&connect_timeout=30\": net/url: invalid userinfo`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1=1;' is not valid. Type of value is not 'integer'`,
    ],
  },
  {
    input: `SELECT username FROM users WHERE id = '1' AND 1/0;`,
    errors: [
      `Value is not valid`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"table\\\">>,<<\\\"funnel\\\">>,<<\\\"column\\\">>,<<\\\"pie\\\">>]\">> or Key 'sort' is required`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1/0;' is not valid. Type of value is not 'list'`,
      `Company SELECT username FROM users WHERE id = '1' AND 1/0; does not exists`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">> or Key 'conv_id' is required`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"db_call\\\">>]\">>`,
      `sql: ping: parse \"******************************* username FROM users WHERE id = '1' AND 1/0;:5432/dbcall?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `sql: ping: parse \"postgres://SELECT username FROM users WHERE id = '1' AND 1/0;:<EMAIL>:5432/dbcall?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `sql: ping: parse \"postgres://postgres:SELECT username FROM users WHERE id = '1' AND 1/0;@COR-6938.middleware.loc:5432/dbcall?sslmode=disable&connect_timeout=30\": invalid port \":SELECT username FROM users WHERE id = '1' AND 1\" after host`,
      `sql: ping: pq: SSL is not enabled on the server`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"api_key\\\">>,<<\\\"group\\\">>,<<\\\"shared\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
      `Invalid dbcall instance`,
      `Value is not valid email`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"google\\\">>,<<\\\"corezoid\\\">>]\">>`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1/0;' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"corezoid\">>]}]}'`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Access denied`,
      `Params are not valid`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1/0;' is not valid. Type of value is not 'list' or Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
      `privs are not valid`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1/0;' is not valid. Type of value is not '{list,[{type,binary},{allowed_values,[<<\"support2\">>,<<\"corezoid\">>]}]}'`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1/0;' is not valid project_id or Key 'project_short_name' is required`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1/0;' is not valid stage_id or Key 'stage_short_name' is required`,
      `obj_id is not found`,
      `Viber responce: invalidAuthToken`,
      `result does not coincide with one of the expected values [error]`,
      `invalid mode`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Object alias not found`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>,<<\\\"tacts\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"name\\\">>,<<\\\"title\\\">>,<<\\\"owner\\\">>,<<\\\"date\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"conv\\\">>,<<\\\"conveyor\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"my\\\">>,<<\\\"shared\\\">>,<<\\\"favorites\\\">>,<<\\\"recent\\\">>,\\n <<\\\"deleted\\\">>]\">>`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>]\">>`,
      `limit has not valid type, expected types [integer]`,
      `invalid email`,
      `Value '<<\"SELECT username FROM users WHERE id = '1' AND 1/0;\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"alias\\\">>,\\n <<\\\"version\\\">>,<<\\\"config\\\">>,<<\\\"stage\\\">>,<<\\\"project\\\">>,<<\\\"group\\\">>]\">>`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `sql: ping: parse \"*********************************************** username FROM users WHERE id = '1' AND 1/0;:5432/test?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `sql: ping: parse \"postgres://test_user:SELECT username FROM users WHERE id = '1' AND 1/0;@**********:5432/test?sslmode=disable&connect_timeout=30\": invalid port \":SELECT username FROM users WHERE id = '1' AND 1\" after host`,
      `sql: ping: parse \"postgres://SELECT username FROM users WHERE id = '1' AND 1/0;:3sha1cheeH4uoziej1u@**********:5432/test?sslmode=disable&connect_timeout=30\": invalid character \" \" in host name`,
      `Value 'SELECT username FROM users WHERE id = '1' AND 1/0;' is not valid. Type of value is not 'integer'`,
    ],
  },
];

export const maxLength = [
  {
    input: faker.random.alphaNumeric(2001),
    errors: [
      `Key 'obj_type' is required or Key 'sort' is required`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 250`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 255`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
      `Value is not valid`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 100`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 510`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 1000`,
      `pattern more than maximum allowed size (range) 150`,
      `obj_id is not found`,
      `token more than maximum allowed size (range) 512`,
      `result does not coincide with one of the expected values [error]`,
      `Unexpected logic type`,
      `invalid conditions`,
      `invalid timer value`,
      `to small timer value, min must be 10`,
      `invalid ConvId`,
      `invalid mode`,
      `check result data types`,
      `missed parametrs`,
      `max threads must be int`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 150`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Incorrect stages, value must be a list of objects, title format ^[a-z0-9-]*$, immutable is boolean`,
      `Invalid value of the 'data' key`,
      `Wrong object reference. Validation error`,
      `Object alias not found`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 24`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required or Key 'folder_id' is required`,
      `Project or stage mismatch`,
      `user wasn't found for these login, login_type`,
      `key: data.entity_type_name.Value is not valid. Value's byte_size is more than maximum allowed: 200`,
      `bad limit`,
      `bad offset`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 36`,
      `node_id more than maximum allowed size (range) 24`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'obj_id' is required`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 128`,
      `{\"description\":\"Value is not valid. Value's byte_size is more than maximum allowed: 255\"`,
      `Value is not valid. Value's byte_size is more than maximum allowed: 5000`,
      `timezone_offset has not valid type, expected types [integer, binary_integer]`,
      `timestamp has not valid type, expected types [boolean]`,
      `invalid extra types`,
      `invalid value response or response_type, or both`,
      `invalid value result data types`,
    ],
  },
];

export const minLength = [
  {
    input: '',
    errors: [
      `Value is not valid. Value's byte_size is less than minimum allowed: 1`,
      `Value is not valid`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 3`,
      `Value is not valid. Value's byte_size is less than minimum allowed: 1`,
      `Value 'null' is not valid. Type of value is not 'binary'`,
      `Key 'title' is required`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required or Key 'folder_id' is required`,
      `Couldnt link  to alias or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
      `key: data.entity_type_name.Value is not valid. Value's byte_size is less than minimum allowed: 1`,
      `bad limit`,
      `node_id less than minimum allowed size (range) 24`,
      `timezone_offset has not valid type, expected types [integer, binary_integer]`,
    ],
  },
];
