#!/usr/bin/env python3
"""
Анализ сырых JSON WebSocket сообщений с индивидуальным разделением по процессам
"""

import json
import re
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Any
import argparse

def parse_raw_json_messages(log_file_path: str) -> Dict[str, List[Dict]]:
    """
    Парсинг сырых JSON сообщений из лог файла
    """
    messages = defaultdict(list)
    
    with open(log_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            # Ищем строки с RAW_JSON
            if '📨 RAW_JSON' in line:
                # Паттерн для извлечения JSON: timestamp + JSON
                match = re.search(r'RAW_JSON #(\d+) \[([^\]]+)\]: (.+)', line)
                if match:
                    msg_num = int(match.group(1))
                    timestamp_str = match.group(2)
                    json_str = match.group(3)
                    
                    try:
                        # Парсим JSON
                        json_data = json.loads(json_str)
                        
                        # Извлекаем информацию о процессе и типе сообщения
                        if json_data.get('request_proc') == 'ok' and json_data.get('ops'):
                            op = json_data['ops'][0]
                            msg_type = op.get('type')
                            msg_obj = op.get('obj')
                            
                            # Получаем идентификатор процесса
                            process_id = None
                            if 'obj_id' in op:
                                process_id = op['obj_id']
                            elif 'conv_id' in op:
                                process_id = op['conv_id']
                            
                            if process_id and msg_type and msg_obj:
                                # Создаем ключ для типа сообщения
                                if msg_type == 'monitor_stat' and msg_obj == 'graph_node':
                                    msg_key = 'graph_node'
                                elif msg_type == 'monitor_stat' and msg_obj == 'graph_tacts':
                                    msg_key = 'graph_tacts'
                                elif msg_type == 'monitor_list' and msg_obj == 'conv':
                                    msg_key = 'conv_list'
                                else:
                                    continue
                                
                                # Добавляем сообщение в соответствующий список
                                messages[msg_key].append({
                                    'msg_num': msg_num,
                                    'timestamp': timestamp_str,
                                    'timestamp_ms': datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).timestamp() * 1000,
                                    'process_id': process_id,
                                    'type': msg_type,
                                    'obj': msg_obj,
                                    'data': op
                                })
                    except json.JSONDecodeError:
                        continue
    
    return messages

def analyze_intervals_by_process(messages: Dict[str, List[Dict]]) -> Dict[str, Dict]:
    """
    Анализ интервалов между сообщениями для каждого процесса отдельно
    """
    analysis = {}
    
    for msg_type, msg_list in messages.items():
        # Группируем сообщения по процессам
        by_process = defaultdict(list)
        for msg in msg_list:
            by_process[msg['process_id']].append(msg)
        
        # Анализируем каждый процесс отдельно
        process_analysis = {}
        for process_id, process_messages in by_process.items():
            # Сортируем по времени
            process_messages.sort(key=lambda x: x['timestamp_ms'])
            
            intervals = []
            for i in range(1, len(process_messages)):
                interval = process_messages[i]['timestamp_ms'] - process_messages[i-1]['timestamp_ms']
                intervals.append(interval)
            
            if intervals:
                # Исключаем аномальный интервал 10475ms как указано пользователем
                filtered_intervals = [i for i in intervals if i != 10475]
                
                if filtered_intervals:
                    process_analysis[f'process_{process_id}'] = {
                        'total_messages': len(process_messages),
                        'messages_with_intervals': len(filtered_intervals),
                        'min_interval_ms': min(filtered_intervals),
                        'max_interval_ms': max(filtered_intervals),
                        'avg_interval_ms': round(sum(filtered_intervals) / len(filtered_intervals), 1),
                        'median_interval_ms': sorted(filtered_intervals)[len(filtered_intervals) // 2],
                        'long_gaps_count': len([i for i in filtered_intervals if i > 2000]),
                        'long_gaps_ms': [i for i in filtered_intervals if i > 2000],
                        'first_message_time': process_messages[0]['timestamp'],
                        'last_message_time': process_messages[-1]['timestamp']
                    }
                    
                    # Дополнительные поля для conv_list
                    if msg_type == 'conv_list':
                        final_counts = []
                        for msg in process_messages:
                            if msg['data'].get('list'):
                                for item in msg['data']['list']:
                                    if item.get('title') == 'Final':
                                        final_counts.append(item.get('count', 0))
                        
                        if final_counts:
                            process_analysis[f'process_{process_id}']['final_count_min'] = min(final_counts)
                            process_analysis[f'process_{process_id}']['final_count_max'] = max(final_counts)
                            process_analysis[f'process_{process_id}']['final_count_avg'] = round(sum(final_counts) / len(final_counts), 1)
        
        analysis[msg_type] = process_analysis
    
    return analysis

def main():
    parser = argparse.ArgumentParser(description='Анализ сырых JSON WebSocket сообщений')
    parser.add_argument('log_file', help='Путь к лог файлу WebSocket')
    parser.add_argument('--output', '-o', help='Путь для сохранения JSON результата')
    
    args = parser.parse_args()
    
    print(f"🔍 Анализ сырых JSON сообщений из файла: {args.log_file}")
    
    # Парсим сырые JSON сообщения
    messages = parse_raw_json_messages(args.log_file)
    
    print(f"📊 Найдено сообщений:")
    for msg_type, msg_list in messages.items():
        processes = set(msg['process_id'] for msg in msg_list)
        print(f"   {msg_type}: {len(msg_list)} сообщений от {len(processes)} процессов")
    
    # Анализируем интервалы по каждому процессу
    analysis = analyze_intervals_by_process(messages)
    
    # Выводим результаты
    print("\n" + "="*80)
    print("📈 РЕЗУЛЬТАТЫ АНАЛИЗА ПО КАЖДОМУ ПРОЦЕССУ")
    print("="*80)
    
    for msg_type, process_data in analysis.items():
        print(f"\n🔹 {msg_type.upper()}")
        print("-" * 60)
        
        if not process_data:
            print("   ❌ Нет данных для анализа")
            continue
            
        for process_name, stats in process_data.items():
            print(f"\n   📍 {process_name}")
            print(f"      Сообщений: {stats['total_messages']}")
            print(f"      Интервалов: {stats['messages_with_intervals']}")
            print(f"      Мин интервал: {stats['min_interval_ms']:.0f}ms")
            print(f"      Макс интервал: {stats['max_interval_ms']:.0f}ms")
            print(f"      Средний интервал: {stats['avg_interval_ms']:.1f}ms")
            print(f"      Медиана: {stats['median_interval_ms']:.0f}ms")
            
            if stats['long_gaps_count'] > 0:
                print(f"      ⚠️  Длинные паузы (>2s): {stats['long_gaps_count']} шт.")
                print(f"      ⚠️  Значения пауз: {stats['long_gaps_ms']} ms")
            else:
                print(f"      ✅ Длинных пауз (>2s): нет")
            
            if 'final_count_min' in stats:
                print(f"      📊 Final count: {stats['final_count_min']}-{stats['final_count_max']} (среднее: {stats['final_count_avg']:.1f})")
            
            print(f"      🕐 Период: {stats['first_message_time']} - {stats['last_message_time']}")
    
    # Сохраняем результат в JSON файл
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Результаты сохранены в: {args.output}")
    
    print("\n🎯 Анализ завершен!")

if __name__ == "__main__":
    main()