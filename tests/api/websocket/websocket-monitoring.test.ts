import { application } from '../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../infrastructure/model/ApiKey';
import { WebSocketClient } from '../../../application/api/websocketUtils';
import { User } from '../../../infrastructure/model/User';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import dataConvCode from '../../K6/data/dataConvCode.json';
import { debug } from '../../../support/utils/logger';
import * as fs from 'fs';
import * as path from 'path';

describe('WebSocket monitoring for load testing', () => {
  let apikey: ApiKey;
  let companyId: any;
  let wsClient: WebSocketClient;
  let cookie: string;
  let user: User;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    companyId = apikey.companies[0].id;
    const origin = ConfigurationManager.getConfiguration().getApiUrl();
    const [ws_url, ws_path] = ConfigurationManager.getConfiguration().getWsUrl();

    user = await application.getAuthorizedUser({ company: {} }, 6);
    cookie = user.cookieUser;

    wsClient = new WebSocketClient(ws_url, ws_path, cookie, origin, 300000); // 5 минут без переподключения
    await wsClient.connect();
  });

  afterAll(async () => {
    if (wsClient) {
      wsClient.close();
    }
  });

  test('Monitor all processes during 5-minute load testing', async () => {
    const monitoringEndpoints = [
      { type: 'monitor_stat', obj: 'graph_tacts' },
      { type: 'monitor_list', obj: 'conv' },
      { type: 'monitor_stat', obj: 'graph_node' },
    ];

    const processesToMonitor = dataConvCode; // Используем полные объекты с node_id
    console.log(`🚀 Starting 5-minute monitoring session for ${processesToMonitor.length} processes`);

    let messagesReceived = 0;
    let subscriptionsCreated = 0;

    // Счетчики и временные метки для каждого типа сообщений
    const messageStats = {
      graph_node: { count: 0, lastTime: 0, intervals: [] as number[] },
      graph_tacts: { count: 0, lastTime: 0, intervals: [] as number[] },
      conv_list: { count: 0, lastTime: 0, intervals: [] as number[] },
    };

    // Создаем файл для детальных логов
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const logDir = path.join(process.cwd(), 'report', 'websocket');
    const logFile = path.join(logDir, `websocket-monitoring-${timestamp}.log`);

    // Создаем директорию если не существует
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Функция для записи в файл и краткого вывода в консоль
    const logToFile = (message: string, consoleMessage?: string): void => {
      fs.appendFileSync(logFile, `${new Date().toISOString()} ${message}\n`);
      if (consoleMessage) {
        console.log(consoleMessage);
      }
    };

    logToFile('=== WebSocket Monitoring Session Started ===');
    console.log(`📄 Detailed logs: ${logFile}`);

    // Add direct WebSocket message listener to capture all incoming messages
    const originalWs = (wsClient as any).client;

    const messageListener = (data: any): void => {
      messagesReceived++;
      const currentTime = Date.now();

      try {
        const messageString = data.toString();
        const parsedData = JSON.parse(messageString);

        // Логируем все сырые JSON сообщения
        const timestamp = new Date().toISOString();
        logToFile(`📨 RAW_JSON #${messagesReceived} [${timestamp}]: ${messageString}`);

        // Анализируем только наши типы мониторинга
        if (parsedData.request_proc === 'ok' && parsedData.ops && parsedData.ops[0]) {
          const op = parsedData.ops[0];

          if (op.type === 'monitor_stat' && op.obj === 'graph_node') {
            messageStats.graph_node.count++;
            if (messageStats.graph_node.lastTime > 0) {
              const interval = currentTime - messageStats.graph_node.lastTime;
              messageStats.graph_node.intervals.push(interval);
              logToFile(
                `📈 GRAPH_NODE #${messageStats.graph_node.count}: interval=${interval}ms, in=${op.list?.[0]?.in}, out=${op.list?.[0]?.out}`,
                messageStats.graph_node.count % 10 === 0
                  ? `📈 GRAPH_NODE: ${messageStats.graph_node.count} messages`
                  : undefined,
              );
            } else {
              logToFile(
                `📈 GRAPH_NODE #${messageStats.graph_node.count}: FIRST, in=${op.list?.[0]?.in}, out=${op.list?.[0]?.out}`,
                `📈 GRAPH_NODE: Started`,
              );
            }
            messageStats.graph_node.lastTime = currentTime;
          } else if (op.type === 'monitor_stat' && op.obj === 'graph_tacts') {
            messageStats.graph_tacts.count++;
            if (messageStats.graph_tacts.lastTime > 0) {
              const interval = currentTime - messageStats.graph_tacts.lastTime;
              messageStats.graph_tacts.intervals.push(interval);
              logToFile(
                `🔄 GRAPH_TACTS #${messageStats.graph_tacts.count}: interval=${interval}ms, count=${op.count}`,
                messageStats.graph_tacts.count % 10 === 0
                  ? `🔄 GRAPH_TACTS: ${messageStats.graph_tacts.count} messages`
                  : undefined,
              );
            } else {
              logToFile(
                `🔄 GRAPH_TACTS #${messageStats.graph_tacts.count}: FIRST, count=${op.count}`,
                `🔄 GRAPH_TACTS: Started`,
              );
            }
            messageStats.graph_tacts.lastTime = currentTime;
          } else if (op.type === 'monitor_list' && op.obj === 'conv') {
            messageStats.conv_list.count++;
            if (messageStats.conv_list.lastTime > 0) {
              const interval = currentTime - messageStats.conv_list.lastTime;
              messageStats.conv_list.intervals.push(interval);
              const finalCount = op.list?.find((item: any) => item.title === 'Final')?.count || 0;
              logToFile(
                `📋 CONV_LIST #${messageStats.conv_list.count}: interval=${interval}ms, Final count=${finalCount}`,
                messageStats.conv_list.count % 10 === 0
                  ? `📋 CONV_LIST: ${messageStats.conv_list.count} messages (Final: ${finalCount})`
                  : undefined,
              );
            } else {
              const finalCount = op.list?.find((item: any) => item.title === 'Final')?.count || 0;
              logToFile(
                `📋 CONV_LIST #${messageStats.conv_list.count}: FIRST, Final count=${finalCount}`,
                `📋 CONV_LIST: Started (Final: ${finalCount})`,
              );
            }
            messageStats.conv_list.lastTime = currentTime;
          }
        }
      } catch (err) {
        // Log all raw messages for debugging
        const rawMessage = data.toString();
        logToFile(`📨 Raw WebSocket Message #${messagesReceived}: ${rawMessage}`);

        // Try to parse and log any monitor messages we might be missing
        try {
          const parsed = JSON.parse(rawMessage);
          if (parsed.ops && parsed.ops[0] && parsed.ops[0].type && parsed.ops[0].type.includes('monitor')) {
            logToFile(`🔍 Unparsed Monitor Message: ${JSON.stringify(parsed, null, 2)}`);
          }
        } catch (parseErr) {
          // Ignore parse errors for truly invalid JSON
        }
      }
    };

    // Add listener to the actual WebSocket
    originalWs.on('message', messageListener);

    // Add keep-alive ping every 30 seconds to prevent disconnection
    const keepAliveInterval = setInterval(() => {
      if (originalWs.readyState === originalWs.OPEN) {
        originalWs.ping();
        logToFile('🏓 Sending keep-alive ping');
      }
    }, 30000);

    // Subscribe to monitoring for all processes
    console.log('🔔 Subscribing to monitoring for all processes...');
    for (const process of processesToMonitor) {
      for (const endpoint of monitoringEndpoints) {
        const message = {
          ops: [
            {
              type: endpoint.type,
              obj: endpoint.obj,
              conv_id: endpoint.obj === 'graph_node' ? process.conv_id : undefined, // graph_node использует conv_id
              obj_id: endpoint.obj !== 'graph_node' ? process.conv_id : undefined, // остальные используют obj_id
              node_id: endpoint.obj === 'graph_node' ? process.node_id : undefined,
              company_id: companyId,
              status: 'on',
            },
          ],
        };

        // Remove undefined fields
        if (message.ops[0].node_id === undefined) {
          delete message.ops[0].node_id;
        }
        if (message.ops[0].conv_id === undefined) {
          delete message.ops[0].conv_id;
        }
        if (message.ops[0].obj_id === undefined) {
          delete message.ops[0].obj_id;
        }

        console.log(`🔔 Subscribing to conv_id: ${process.conv_id}, endpoint: ${endpoint.type}/${endpoint.obj}`);
        console.log(`📤 Sending message: ${JSON.stringify(message)}`);

        try {
          // Send subscription without waiting for specific response
          originalWs.send(JSON.stringify(message));
          subscriptionsCreated++;
          console.log(
            `✅ Subscription sent successfully for conv_id: ${process.conv_id}, endpoint: ${endpoint.type}/${endpoint.obj}`,
          );
        } catch (err) {
          console.log(
            `❌ Failed to send subscription for conv_id: ${process.conv_id}, endpoint: ${endpoint.type}/${endpoint.obj}`,
            err,
          );
        }

        // Small delay between subscriptions
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }

    console.log(`✅ Created ${subscriptionsCreated} monitoring subscriptions`);
    console.log(`⏱️ Starting smart monitoring session...`);
    console.log(
      `📊 K6 test: k6 run tests/K6/Load-test/loadCodeEachConv.js (${
        processesToMonitor.length
      } processes × 1 RPS × 60s = ${processesToMonitor.length * 60} requests)`,
    );
    console.log(`🕐 Start K6 in 10 seconds after WebSocket connection is ready`);

    // Wait 10 seconds before starting monitoring to ensure stable connection
    console.log(`⏳ Waiting 10 seconds for stable WebSocket connection...`);
    await new Promise(resolve => setTimeout(resolve, 10000));

    console.log(`🚀 WebSocket connection stable - ready for K6 load test!`);

    // Smart monitoring - continue until no new messages for 20 seconds
    const maxMonitoringDuration = 150 * 1000; // 150 seconds maximum
    const noMessageTimeout = 20 * 1000; // 20 seconds without new messages
    const startTime = Date.now();

    let lastMessageTime = Date.now();
    // const noNewMessagesCount = 0; // Unused variable

    // Override message listener to track last message time
    const originalMessageListener = messageListener;
    const smartMessageListener = (data: any): void => {
      lastMessageTime = Date.now();
      originalMessageListener(data);
    };

    // Replace listener
    originalWs.removeListener('message', messageListener);
    originalWs.on('message', smartMessageListener);

    while (Date.now() - startTime < maxMonitoringDuration) {
      const elapsed = Math.floor((Date.now() - startTime) / 1000);
      const remaining = Math.floor((maxMonitoringDuration - (Date.now() - startTime)) / 1000);
      const timeSinceLastMessage = Date.now() - lastMessageTime;

      // Check if we should stop (no new messages for 20 seconds and at least 120 seconds elapsed)
      if (timeSinceLastMessage > noMessageTimeout && elapsed > 120) {
        console.log(`\n🏁 Stopping monitoring: No new messages for ${Math.floor(timeSinceLastMessage / 1000)}s`);
        break;
      }

      if (elapsed % 20 === 0 && elapsed > 0) {
        // Log every 20 seconds with detailed stats
        console.log(`\n⏰ ═══════════════════════════════════════════════════════════`);
        console.log(`📊 MONITORING STATUS: ${elapsed}s elapsed, ${remaining}s remaining`);
        console.log(`📈 GRAPH_NODE: ${messageStats.graph_node.count} messages`);
        console.log(`🔄 GRAPH_TACTS: ${messageStats.graph_tacts.count} messages`);
        console.log(`📋 CONV_LIST: ${messageStats.conv_list.count} messages`);
        console.log(`📊 Total: ${messagesReceived} messages`);
        console.log(`⏰ Time since last message: ${Math.floor(timeSinceLastMessage / 1000)}s`);

        // Показать последние интервалы для каждого типа
        if (messageStats.graph_node.intervals.length > 0) {
          const lastInterval = messageStats.graph_node.intervals[messageStats.graph_node.intervals.length - 1];
          const avgInterval =
            messageStats.graph_node.intervals.reduce((a, b) => a + b, 0) / messageStats.graph_node.intervals.length;
          console.log(`   📈 Graph Node: last=${lastInterval}ms, avg=${avgInterval.toFixed(0)}ms`);
        }

        if (messageStats.graph_tacts.intervals.length > 0) {
          const lastInterval = messageStats.graph_tacts.intervals[messageStats.graph_tacts.intervals.length - 1];
          const avgInterval =
            messageStats.graph_tacts.intervals.reduce((a, b) => a + b, 0) / messageStats.graph_tacts.intervals.length;
          console.log(`   🔄 Graph Tacts: last=${lastInterval}ms, avg=${avgInterval.toFixed(0)}ms`);
        }

        if (messageStats.conv_list.intervals.length > 0) {
          const lastInterval = messageStats.conv_list.intervals[messageStats.conv_list.intervals.length - 1];
          const avgInterval =
            messageStats.conv_list.intervals.reduce((a, b) => a + b, 0) / messageStats.conv_list.intervals.length;
          console.log(`   📋 Conv List: last=${lastInterval}ms, avg=${avgInterval.toFixed(0)}ms`);
        }
        console.log(`⏰ ═══════════════════════════════════════════════════════════\n`);
      }

      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    }

    // Detailed final analysis to log file
    logToFile('\n🏁 ═══════════════════════════════════════════════════════════');
    logToFile('🎯 MONITORING SESSION COMPLETED!');
    logToFile('🏁 ═══════════════════════════════════════════════════════════');
    logToFile(`📊 Total messages received: ${messagesReceived}`);
    logToFile(`🔔 Total subscriptions created: ${subscriptionsCreated}`);

    logToFile('\n📈 GRAPH_NODE ANALYSIS:');
    logToFile(`   Count: ${messageStats.graph_node.count} messages`);
    if (messageStats.graph_node.intervals.length > 0) {
      const avgInterval =
        messageStats.graph_node.intervals.reduce((a, b) => a + b, 0) / messageStats.graph_node.intervals.length;
      const maxInterval = Math.max(...messageStats.graph_node.intervals);
      const minInterval = Math.min(...messageStats.graph_node.intervals);
      logToFile(`   Average interval: ${avgInterval.toFixed(0)}ms`);
      logToFile(`   Min/Max interval: ${minInterval}ms / ${maxInterval}ms`);

      // Проверка стабильности - не должно быть пропусков > 2 секунд
      const longGaps = messageStats.graph_node.intervals.filter(interval => interval > 2000);
      if (longGaps.length > 0) {
        logToFile(`   ⚠️ Found ${longGaps.length} long gaps (>2s): ${longGaps.join(', ')}ms`);
      } else {
        logToFile(`   ✅ No long gaps (>2s) detected - stable!`);
      }
    }

    logToFile('\n🔄 GRAPH_TACTS ANALYSIS:');
    logToFile(`   Count: ${messageStats.graph_tacts.count} messages`);
    if (messageStats.graph_tacts.intervals.length > 0) {
      const avgInterval =
        messageStats.graph_tacts.intervals.reduce((a, b) => a + b, 0) / messageStats.graph_tacts.intervals.length;
      const maxInterval = Math.max(...messageStats.graph_tacts.intervals);
      const minInterval = Math.min(...messageStats.graph_tacts.intervals);
      logToFile(`   Average interval: ${avgInterval.toFixed(0)}ms`);
      logToFile(`   Min/Max interval: ${minInterval}ms / ${maxInterval}ms`);

      // Проверка частоты - должны приходить при K6 нагрузке (каждые 1-2 секунды)
      if (avgInterval > 3000) {
        logToFile(`   ⚠️ Too slow average interval: ${avgInterval.toFixed(0)}ms (expected <3s with K6 load)`);
      } else {
        logToFile(`   ✅ Good frequency for K6 load testing`);
      }
    } else {
      logToFile(`   ❌ NO MESSAGES - Check if K6 load test is running!`);
    }

    logToFile('\n📋 CONV_LIST ANALYSIS:');
    logToFile(`   Count: ${messageStats.conv_list.count} messages`);
    if (messageStats.conv_list.intervals.length > 0) {
      const avgInterval =
        messageStats.conv_list.intervals.reduce((a, b) => a + b, 0) / messageStats.conv_list.intervals.length;
      const maxInterval = Math.max(...messageStats.conv_list.intervals);
      const minInterval = Math.min(...messageStats.conv_list.intervals);
      logToFile(`   Average interval: ${avgInterval.toFixed(0)}ms`);
      logToFile(`   Min/Max interval: ${minInterval}ms / ${maxInterval}ms`);

      // Проверка частоты - должны приходить при K6 нагрузке (каждые 1-2 секунды)
      if (avgInterval > 3000) {
        logToFile(`   ⚠️ Too slow average interval: ${avgInterval.toFixed(0)}ms (expected <3s with K6 load)`);
      } else {
        logToFile(`   ✅ Good frequency for K6 load testing`);
      }
    } else {
      logToFile(`   ❌ NO MESSAGES - Check if K6 load test is running!`);
    }

    // Summary to console
    console.log(`\n🎯 MONITORING COMPLETED:`);
    console.log(`📊 Total: ${messagesReceived} messages`);
    console.log(`📈 GRAPH_NODE: ${messageStats.graph_node.count}`);
    console.log(`🔄 GRAPH_TACTS: ${messageStats.graph_tacts.count}`);
    console.log(`📋 CONV_LIST: ${messageStats.conv_list.count}`);
    console.log(`📄 Full details: ${logFile}`);

    // Remove message listener and clear keep-alive
    originalWs.removeListener('message', smartMessageListener);
    clearInterval(keepAliveInterval);

    // Проверки для автоматических тестов
    expect(subscriptionsCreated).toBeGreaterThan(0);
    expect(messageStats.graph_node.count).toBeGreaterThan(0);

    // Если запущена K6 нагрузка, должны быть graph_tacts и conv_list
    if (messageStats.graph_tacts.count === 0) {
      console.log(`⚠️ WARNING: No graph_tacts messages - ensure K6 load test is running during monitoring`);
      logToFile(`⚠️ WARNING: No graph_tacts messages - ensure K6 load test is running during monitoring`);
    }
    if (messageStats.conv_list.count === 0) {
      console.log(`⚠️ WARNING: No conv_list messages - ensure K6 load test is running during monitoring`);
      logToFile(`⚠️ WARNING: No conv_list messages - ensure K6 load test is running during monitoring`);
    }

    logToFile('=== WebSocket Monitoring Session Completed ===');
  }, 200000); // 200 seconds timeout (150s monitoring + 50s buffer for full logs)

  test('Test WebSocket connection and basic monitoring', async () => {
    debug('Testing WebSocket connection for monitoring');

    expect(wsClient).toBeDefined();

    const testConvId = dataConvCode[0].conv_id;
    debug(`Using test conv_id: ${testConvId}`);

    debug('WebSocket client is connected and ready for monitoring');
    debug(`Available processes: ${dataConvCode.length}`);
    debug(`First process conv_id: ${testConvId}`);

    debug('WebSocket monitoring functionality is working');
  }, 5000);
});
