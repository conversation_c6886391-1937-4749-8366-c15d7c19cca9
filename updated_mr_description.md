# [COR-12118] Task Parameter Migration - 8 Add_task Tests with Exact Operation Sequences

## Summary
Successfully migrated JMeter "Task Parameter" ThreadGroup from API_v2_Amazon.jmx to Playwright TypeScript tests. The migration correctly implements **8 Add_task tests with their exact operation sequences** as specified by the user, each corresponding to one Add_task block from the JMeter ThreadGroup.

## Migration Approach
- **Source**: API_v2_Amazon.jmx (4.5MB) - ThreadGroup "Task Parameter" starting at line 27206
- **Target**: tests/api2pre/task_parameter.test.ts
- **Structure**: Each test follows exact user-specified operation sequence from JMeter analysis
- **Test Count**: Exactly 8 tests with complete operation flows as requested by user

## Test Implementation Details

### 8 Add_task Test Scenarios with Exact Operation Sequences:
1. **№1 VIEW API 2 Add_task #1**: create task → list node → modify conv_params → list node → modify conv_params → list node
2. **№2 VIEW API 2 Add_task #2**: create task → list node → modify conv_params → list node → modify conv_params → list node  
3. **VIEW API 2 Add_task #3**: create task → list node → modify conv_params → list node → show task
4. **VIEW API 2 Add_task #4**: create task → list node → modify conv_params → list node
5. **VIEW API 2 Add_task #5**: create task → list node → modify conv_params → list node → modify conv_params → list node
6. **№7 VIEW API 2 Add_task #6**: create task → list node → modify conv_params → list node → modify conv_params
7. **VIEW API 2 Add_task #7 (n) wrong_validate_params**: create task → modify conv_params
8. **VIEW API 2 Add_task #8**: create task → list node → Reset_node → list node

### Data Structures Migrated:
- **Test #1 & #2**: Complex nested objects with arrays: `{a: '123', b: 456, s: {f: 567, r: {t: 9870}}, arr: [{v: 567}, {w: 5676}, 678, {q: [{e: 'e'}]}]}`
- **Test #3**: Array structures: `{array: [{test: [{qw: 4567}]}, {test: [{qw: '987'}]}, '6781', {test: [{qw: 123}]}]}`
- **Test #4**: Product lists and booleans: `{productList: ['567', '789', {l: 456}], bool: true, e: {}, f: []}`
- **Test #5**: Multi-dimensional arrays: `{arrey2m: [{arey: [[{ar1: 123}, {ar1: 345}]]}]}`
- **Test #6**: Null values: `{objnull: null}`
- **Test #7 & #8**: Simple test type: `{testtype: 123}`

### Operation Types Implemented:
- **Create Task**: Task creation with various data structures from BeanShell scripts
- **List Node**: Node listing operations for verification
- **Modify Conv_Params**: Parameter configuration with comprehensive flags, types, and nested paths
- **Show Task**: Task retrieval by reference (Test #3)
- **Reset_node**: Node reset operations (Test #8 only)

### Key Migration Patterns:
- **BeanShell to TypeScript**: Converted complex BeanShell parameter generation to TypeScript
- **Operation Sequences**: Each test follows exact user-specified sequence from JMeter analysis
- **Data Structures**: Preserved complex nested objects, arrays, and special cases (null values)
- **Parameter Configurations**: Comprehensive modify conv_params with nested paths, arrays, and object types
- **Assertions**: Migrated JMeter ResponseAssertions to TypeScript expect statements
- **Timing**: Maintained 3-second pauses between tests as per JMeter TestAction elements

## Technical Implementation
- **Setup**: beforeAll creates conveyor and lists nodes (process_node_ID, final_node_ID)
- **Cleanup**: afterAll deletes test conveyor
- **API Client**: Uses ApiKeyClient following established patterns
- **Utilities**: Leverages existing createRequestWithOps, OBJ_TYPE, REQUEST_TYPE constants
- **Type Safety**: Proper TypeScript type casting for node IDs
- **Operation Mapping**: Direct 1:1 mapping from JMeter BeanShell scripts to TypeScript

## Files Changed
- `tests/api2pre/task_parameter.test.ts` - Main test implementation (8 Add_task tests with exact sequences)
- `task_parameter_migration_audit.md` - Comprehensive migration documentation with operation sequences
- `tests/api2pre/README.md` - Directory documentation with detailed operation flows

## Verification
- ✅ Lint passes (npm run lint) - only warnings, no errors
- ✅ Follows established code patterns from existing TaskParameter.test.ts
- ✅ Exactly 8 tests as specified by user requirements
- ✅ Each test follows exact user-specified operation sequence
- ✅ All operation types implemented: create task, list node, modify conv_params, show task, Reset_node
- ✅ Complex data structures accurately migrated from BeanShell scripts
- ✅ Comprehensive parameter configurations for modify conv_params operations

## Migration Quality
- **High Fidelity**: Preserves exact data structures from JMeter BeanShell scripts
- **Exact Sequences**: Implements user-specified exact operation sequences for each test
- **Pattern Compliance**: Follows established codebase conventions
- **User Requirements**: Meets exact specification of 8 Add_task tests with complete operation flows
- **Documentation**: Comprehensive audit trail and implementation notes with operation sequence details

---
**Link to Devin run**: https://app.devin.ai/sessions/b36f57914d5149c0a186e3486553bfea  
**Requested by**: <EMAIL>  
**Co-author**: NightShift
